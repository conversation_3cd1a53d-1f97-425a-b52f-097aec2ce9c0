const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'database', 'users.db');

// Check if database exists
const fs = require('fs');
if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found. Please run "npm run init-db" first.');
    process.exit(1);
}

// Connect to database
const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
        process.exit(1);
    } else {
        console.log('📊 Connected to database. Fetching users...\n');
        viewUsers();
    }
});

function viewUsers() {
    // Get all users
    const query = `
        SELECT 
            id,
            name,
            email,
            registration_date,
            last_login,
            is_active,
            email_verified
        FROM users 
        ORDER BY registration_date DESC
    `;

    db.all(query, [], (err, rows) => {
        if (err) {
            console.error('Error fetching users:', err.message);
            return;
        }

        if (rows.length === 0) {
            console.log('📭 No users found in the database.');
            console.log('💡 Users will appear here after they register on your website.');
        } else {
            console.log(`👥 Found ${rows.length} user(s):\n`);
            console.log('═'.repeat(100));
            
            rows.forEach((user, index) => {
                console.log(`👤 User #${index + 1}`);
                console.log(`   ID: ${user.id}`);
                console.log(`   Name: ${user.name}`);
                console.log(`   Email: ${user.email}`);
                console.log(`   Registered: ${formatDate(user.registration_date)}`);
                console.log(`   Last Login: ${user.last_login ? formatDate(user.last_login) : 'Never'}`);
                console.log(`   Status: ${user.is_active ? '✅ Active' : '❌ Inactive'}`);
                console.log(`   Email Verified: ${user.email_verified ? '✅ Yes' : '❌ No'}`);
                console.log('─'.repeat(50));
            });
        }

        // Get session count
        getSessionCount();
    });
}

function getSessionCount() {
    const sessionQuery = `
        SELECT COUNT(*) as session_count,
               COUNT(CASE WHEN expires_at > datetime('now') THEN 1 END) as active_sessions
        FROM user_sessions
    `;

    db.get(sessionQuery, [], (err, row) => {
        if (err) {
            console.error('Error fetching session data:', err.message);
        } else {
            console.log(`\n🔐 Session Statistics:`);
            console.log(`   Total Sessions: ${row.session_count}`);
            console.log(`   Active Sessions: ${row.active_sessions}`);
        }

        // Close database
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err.message);
            } else {
                console.log('\n✅ Database connection closed.');
                console.log('\n💡 Tips:');
                console.log('   • Run "npm run view-users" anytime to see updated user list');
                console.log('   • Run "npm run user-stats" for detailed statistics');
                console.log('   • Use a SQLite browser for advanced database management');
            }
        });
    });
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    let timeAgo = '';
    if (diffDays > 0) {
        timeAgo = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
        timeAgo = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
        timeAgo = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
        timeAgo = 'Just now';
    }

    return `${date.toLocaleString()} (${timeAgo})`;
}

// Handle errors
process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
