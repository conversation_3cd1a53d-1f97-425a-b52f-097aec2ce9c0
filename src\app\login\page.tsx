'use client'

import { useState } from 'react'
import Link from 'next/link'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle login logic here
    console.log('Login attempt:', { email, password })
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      {/* Floating magnet decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-8 h-8 bg-magnet-red rounded-full opacity-20 animate-bounce"></div>
        <div className="absolute top-40 right-20 w-6 h-6 bg-magnet-blue rounded-full opacity-30 animate-pulse"></div>
        <div className="absolute bottom-32 left-1/4 w-10 h-10 bg-magnet-gold rounded-full opacity-25 animate-bounce delay-300"></div>
        <div className="absolute bottom-20 right-1/3 w-4 h-4 bg-magnet-silver rounded-full opacity-40 animate-pulse delay-500"></div>
      </div>

      <div className="magnet-card p-8 w-full max-w-md relative">
        {/* Logo/Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-magnet-gradient rounded-full mb-4">
            <span className="text-2xl font-bold text-white">🧲</span>
          </div>
          <h1 className="text-3xl font-bold text-magnet-dark mb-2">Julia's Magnets</h1>
          <p className="text-gray-600">Welcome back! Sign in to your account</p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="magnet-input"
              placeholder="Enter your email"
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="magnet-input"
              placeholder="Enter your password"
              required
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input type="checkbox" className="rounded border-gray-300 text-magnet-blue focus:ring-magnet-blue" />
              <span className="ml-2 text-sm text-gray-600">Remember me</span>
            </label>
            <Link href="/forgot-password" className="text-sm text-magnet-blue hover:text-magnet-red transition-colors">
              Forgot password?
            </Link>
          </div>

          <button type="submit" className="magnet-button w-full">
            Sign In
          </button>
        </form>

        {/* Divider */}
        <div className="my-6 flex items-center">
          <div className="flex-1 border-t border-gray-300"></div>
          <span className="px-4 text-sm text-gray-500">or</span>
          <div className="flex-1 border-t border-gray-300"></div>
        </div>

        {/* Sign Up Link */}
        <div className="text-center">
          <p className="text-gray-600">
            Don't have an account?{' '}
            <Link href="/signup" className="text-magnet-blue hover:text-magnet-red font-semibold transition-colors">
              Create one now
            </Link>
          </p>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-magnet-red rounded-full opacity-60"></div>
        <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-magnet-blue rounded-full opacity-40"></div>
      </div>
    </div>
  )
}
