# 📦 Archive Folder

This folder contains archived files and components that are not currently used in the main website but may be useful for future reference.

## 📁 Contents

### `react-components/`
Contains React/Next.js components that were previously created but are not used in the current HTML/CSS/JavaScript implementation:

- `globals.css` - Global styles for React components
- `layout.tsx` - Next.js layout component
- `page.tsx` - Main page React component
- `login-page.tsx` - React login page component

## 📝 Notes

- These files were moved here during project organization
- The current website uses HTML/CSS/JavaScript instead of React
- These components could be useful if migrating to React/Next.js in the future
- All files are preserved for reference and potential future use

## 🔄 Current Implementation

The active website uses:
- **HTML pages** in `Html pages/` folder
- **CSS styles** in `Style/` folder  
- **JavaScript** in `js/` folder
- **Node.js/Express** backend with SQLite database

These archived React components represent an alternative implementation approach.
