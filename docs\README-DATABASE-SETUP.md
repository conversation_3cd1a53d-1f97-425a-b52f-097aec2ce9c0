# 🗄️ Database Setup Guide - <PERSON>'s Magnet Website

This guide will help you set up the database-driven authentication system for your magnet website.

## 📋 Prerequisites

Before starting, make sure you have:
- **Node.js** (version 14 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- A terminal/command prompt

## 🚀 Quick Setup

### 1. Install Dependencies

Open your terminal in the project directory and run:

```bash
npm install
```

This will install all required packages:
- `express` - Web server framework
- `sqlite3` - Database driver
- `bcryptjs` - Password hashing
- `jsonwebtoken` - Authentication tokens
- `cors` - Cross-origin requests
- `helmet` - Security middleware
- And more...

### 2. Initialize the Database

Run the database initialization script:

```bash
npm run init-db
```

This will:
- ✅ Create the `database` folder
- ✅ Create `users.db` SQLite database
- ✅ Set up the `users` and `user_sessions` tables
- ✅ Create database indexes for performance
- ✅ Insert a test user account

**Test Account Created:**
- Email: `<EMAIL>`
- Password: `password123`

### 3. Configure Environment

The `.env` file contains important settings:

```env
PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
DB_PATH=./database/users.db
```

**⚠️ IMPORTANT:** Change the `JWT_SECRET` to a long, random string in production!

### 4. Start the Server

```bash
npm start
```

Or for development with auto-restart:

```bash
npm run dev
```

You should see:
```
🚀 Server running on http://localhost:3000
📁 Database: ./database/users.db
🔒 JWT Secret: your-super...
```

## 🌐 Using the System

### Access Your Website

1. Open your browser and go to: `http://localhost:3000`
2. Navigate to the login page
3. Try logging in with the test account or create a new account

### API Endpoints

Your server provides these endpoints:

- `POST /api/register` - Create new user account
- `POST /api/login` - User authentication
- `GET /api/verify` - Verify authentication token
- `POST /api/logout` - User logout
- `GET /api/profile` - Get user profile
- `GET /api/health` - Server health check

## 🔒 Security Features

### Password Security
- ✅ Passwords are hashed with bcrypt (12 rounds)
- ✅ Never stored in plain text
- ✅ Minimum 6 characters required

### Authentication
- ✅ JWT tokens for secure sessions
- ✅ Token expiration (24h default, 30d with "Remember Me")
- ✅ Server-side session tracking

### API Security
- ✅ Rate limiting (5 auth attempts per 15 minutes)
- ✅ Input validation and sanitization
- ✅ CORS protection
- ✅ Helmet security headers

### Database Security
- ✅ SQL injection prevention
- ✅ Parameterized queries
- ✅ Database indexes for performance

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT 1,
    email_verified BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Sessions Table
```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token TEXT NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

## 🛠️ Troubleshooting

### Common Issues

**1. "Cannot find module" errors**
```bash
npm install
```

**2. Database permission errors**
- Make sure the `database` folder is writable
- Run: `chmod 755 database` (on Mac/Linux)

**3. Port already in use**
- Change the PORT in `.env` file
- Or stop other services using port 3000

**4. JWT token errors**
- Clear browser localStorage
- Restart the server

### Database Management

**View database contents:**
```bash
sqlite3 database/users.db
.tables
SELECT * FROM users;
.quit
```

**Reset database:**
```bash
npm run init-db
```

**Backup database:**
```bash
cp database/users.db database/users_backup.db
```

## 🔄 Migration from localStorage

The new system automatically handles the transition:

1. **Existing localStorage data** is ignored
2. **New registrations** go to the database
3. **All logins** authenticate against the database
4. **UI/UX remains exactly the same**

## 📁 File Structure

```
your-project/
├── database/
│   └── users.db              # SQLite database
├── js/
│   ├── login.js              # Updated frontend auth
│   └── auth-utils.js         # Updated auth utilities
├── scripts/
│   └── init-database.js      # Database setup script
├── server.js                 # Express server
├── package.json              # Dependencies
├── .env                      # Configuration
└── docs/
    └── README-DATABASE-SETUP.md  # This guide
```

## 🚀 Production Deployment

For production deployment:

1. **Change JWT_SECRET** to a secure random string
2. **Set NODE_ENV=production** in .env
3. **Use HTTPS** for secure token transmission
4. **Regular database backups**
5. **Monitor server logs**

## 📞 Support

If you encounter any issues:

1. Check the server console for error messages
2. Verify all dependencies are installed
3. Ensure the database file has proper permissions
4. Check that no other service is using port 3000

The system maintains all your existing UI/UX while providing secure, scalable database authentication!
