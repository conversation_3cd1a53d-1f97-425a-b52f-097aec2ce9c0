/* Customize Page Styles */

/* Main container */
.customize-container {
    display: flex;
    max-width: 1200px;
    margin: 40px auto 0;
    padding: 0 20px;
    gap: 40px;
    box-sizing: border-box;
    justify-content: center;
}

/* Product preview section */
.product-preview {
    flex: 0 0 55%;
    min-width: 300px;
    box-sizing: border-box;
    padding-right: 20px;
}

.preview-area {
    background: #f9f9f9;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.magnet-display {
    background: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.magnet-base {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.magnet-template {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.upload-area-container {
    position: relative;
    width: 100%;
    height: 354.33px; /* 11cm at 96dpi */
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
}

.upload-placeholder {
    text-align: center;
    color: #6c757d;
    padding: 20px;
}

.upload-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #adb5bd;
}

.upload-placeholder p {
    margin: 5px 0;
}

.dimensions {
    font-size: 0.9em;
    color: #adb5bd;
}

/* Father's Day Image Styles */
.fathers-day-image {
    width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.template-bottom {
    padding: 10px;
    background: #fff;
}

.template-design {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.template-image {
    width: 100%;
    height: auto;
    display: block;
}

.image-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    overflow: hidden;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-bottom {
    width: 100%;
    height: auto;
    background: #f8f9fa;
    padding: 0;
    box-sizing: border-box;
    display: block;
}

.template-design {
    width: 100%;
    height: auto;
    display: block;
}

.blue-circle {
    width: 220px;
    height: 220px;
    background: #1e3a8a;
    border-radius: 50%;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

.blue-circle span {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 15px;
}

.father-child-icon {
    font-size: 2.5em;
    display: flex;
    gap: 10px;
}

.preview-controls {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.preview-controls button {
    padding: 8px 15px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.preview-controls button:hover {
    background: #e0e0e0;
}

/* Customization options section */
.customization-options {
    flex: 0 0 35%;
    min-width: 300px;
    max-width: 400px;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.option-section {
    background: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    width: 100%;
    box-sizing: border-box;
    margin-left: 0;
}

.option-section h3 {
    margin-bottom: 15px;
    font-size: 1.2em;
    color: #333;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    width: 100%;
    box-sizing: border-box;
}

.upload-area:hover {
    border-color: #999;
    background: #f0f0f0;
}

.upload-area i {
    font-size: 40px;
    color: #666;
    margin-bottom: 10px;
}

.upload-area p {
    margin: 10px 0 0;
    color: #666;
}

.color-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: transform 0.2s, border-color 0.2s;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    border-color: #333;
    transform: scale(1.1);
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Language Options */
.language-options {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.language-option {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    cursor: pointer;
    background: #f9f9f9;
    transition: all 0.3s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.language-option.selected {
    background: #333;
    color: white;
    border-color: #333;
}

.language-option:hover {
    background: #eee;
}

.language-option.selected:hover {
    background: #222;
}

.quantity-selector button {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: #f9f9f9;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-selector input {
    width: 60px;
    padding: 5px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.price-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.price {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.add-to-cart-btn {
    background: #ff6b8b;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-to-cart-btn:hover {
    background: #ff4d73;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 139, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .customize-container {
        flex-direction: column;
    }
    
    .product-preview,
    .customization-options {
        width: 100%;
        max-width: 100%;
    }
    
    .magnet-base {
        width: 250px;
        height: 250px;
    }
}

/* Form controls */
.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #ff6b8b;
    box-shadow: 0 0 0 2px rgba(255, 107, 139, 0.2);
}
