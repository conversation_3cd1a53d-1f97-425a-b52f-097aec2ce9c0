// Simple loader functionality
window.addEventListener('load', function() {
    const loader = document.querySelector('.loader');
    
    // Hide loader after a short delay
    setTimeout(() => {
        loader.classList.add('hidden');
        // Just hide the loader instead of removing it from DOM
        // This prevents favicon from disappearing
    }, 1000);
});

document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('navMenu');
    const searchToggle = document.getElementById('searchToggle');
    const searchContainer = document.getElementById('searchContainer');
    const searchInput = document.querySelector('.search-input');
    let overlay = document.querySelector('.overlay');
    
    // Create overlay if it doesn't exist
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'overlay';
        document.body.appendChild(overlay);
    }

    // Toggle menu function
    function toggleMenu() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
        overlay.classList.toggle('active');
        
        // Close search if open when toggling menu
        if (searchContainer.classList.contains('active')) {
            searchContainer.classList.remove('active');
            searchToggle.classList.remove('active');
            searchInput.value = '';
            searchInput.blur();
        } else if (navMenu.classList.contains('active') && document.activeElement === searchInput) {
            // If opening menu and search input is focused, blur it
            searchInput.blur();
        }
        
        // Toggle body scroll
        document.body.style.overflow = navMenu.classList.contains('active') ? 'hidden' : '';
    }

    // Toggle search function
    function toggleSearch() {
        searchContainer.classList.toggle('active');
        searchToggle.classList.toggle('active');
        
        // Close menu if open when toggling search
        if (navMenu.classList.contains('active')) {
            toggleMenu();
        }
        
        // Focus on input when search is activated
        if (searchContainer.classList.contains('active')) {
            setTimeout(() => {
                searchInput.focus();
            }, 300);
        }
    }

    // Close search when clicking outside
    function handleClickOutside(event) {
        if (!searchContainer.contains(event.target) && !searchToggle.contains(event.target)) {
            if (searchContainer.classList.contains('active')) {
                searchContainer.classList.remove('active');
                searchToggle.classList.remove('active');
                searchInput.value = '';
            }
        }
    }

    // Event listeners
    hamburger.addEventListener('click', toggleMenu);
    searchToggle.addEventListener('click', toggleSearch);
    document.addEventListener('click', handleClickOutside);
    overlay.addEventListener('click', toggleMenu);

    // Close menu when clicking on a nav link
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navMenu.classList.contains('active')) {
                toggleMenu();
            }
        });
    });

    // Close menu when pressing Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            if (navMenu.classList.contains('active')) {
                toggleMenu();
            }
            if (searchContainer.classList.contains('active')) {
                searchContainer.classList.remove('active');
                searchToggle.classList.remove('active');
                searchInput.value = '';
            }
        }
    });

    // Handle search submission (you can add your search logic here)
    // The keypress event for search input is now handled exclusively by search.js
    // This ensures consistent behavior and avoids duplicate logic.
    // If you need additional logic here, consider triggering custom events or
    // extending the functionality within search.js.
});