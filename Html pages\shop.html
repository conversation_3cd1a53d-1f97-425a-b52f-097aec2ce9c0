<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop - With Love Handmade Gifts</title>
    <link rel="stylesheet" href="../Style/styles.css">
    <link rel="stylesheet" href="../Style/shop.css">
    <link rel="stylesheet" href="../Style/products.css">
    <link rel="stylesheet" href="../Style/modal.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Favicons -->
    <link rel="icon" type="image/png" href="../images/logo.png?ver=1.0" sizes="32x32">
    <link rel="icon" type="image/png" href="../images/logo.png?ver=1.0" sizes="16x16">
    <link rel="shortcut icon" href="../images/logo.png?ver=1.0">
    <!-- Apple Touch Icon (for iOS devices) -->
    <link rel="apple-touch-icon" href="../images/logo.png">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <img src="../images/logo.png" alt="With Love Logo">
            </div>
            <div class="title">With Love</div>
           <div class="header-right">
               <div class="search">
                   <button class="search-toggle" id="searchToggle" aria-label="Search">
                        Search
                    </button>
                    <div class="search-container" id="searchContainer">
                        <div class="search-input-wrapper">
                            <input type="text" id="searchInput" class="search-input" placeholder="Search for products..." autocomplete="off">
                            <i class="fas fa-search search-icon"></i>
                            <div id="searchSuggestions" class="search-suggestions"></div>
                        </div>
                    </div>
               </div>
               <div class="header-icons">
                <a href="../Html pages/wishlist.html" class="wishlist-icon" aria-label="Wishlist">
                    <i class="far fa-heart"></i>
                    <span class="icon-badge wishlist-count">0</span>
                </a>
                <a href="../Html pages/cart.html" class="cart-icon" aria-label="Shopping Cart">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="icon-badge cart-count">0</span>
                </a>
                <div class="hamburger" id="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
           </div>
            <nav class="nav-menu" id="navMenu">
                <ul>
                    <li><a href="/Html pages/index.html">Home</a></li>
                    <li><a href="/Html pages/about.html">About</a></li>
                    <li><a href="/Html pages/shop.html">Shop</a></li>
                    <li><a href="/Html pages/contact.html">Contact</a></li>
                    <li><a href="/Html pages/login.html">Login</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <section class="shop-intro">
                <h1>Our Handmade Treasures</h1>
                <p>Each piece in our collection is crafted with love and attention to detail. Find the perfect handmade gift for your loved ones or treat yourself to something special.</p>
            </section>

            <section class="products">
                <div class="filters">
                    <button class="filter-btn active" data-filter="all">All Products</button>
                    <button class="filter-btn" data-filter="fathers-day">Father's Day</button>
                    <button class="filter-btn" data-filter="calendar">Calendars</button>
                </div>
                <div class="product-grid">
                    <!-- Products will be loaded here by shop.js -->
                </div>
            </section>
        </main>

         <footer>
            <div class="contact">
                <h3>Contact Us</h3>
                <p><a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank" rel="noopener noreferrer"><i class="fas fa-envelope"></i> <EMAIL></a></p>
                <p><a href="tel:+35679652171"><i class="fas fa-phone-alt"></i> +356 79652171</a></p>
            </div>
            <div class="social">
                <h3>Follow Us</h3>
                <div class="social-icons">
                    <a href="https://www.facebook.com" target="_blank" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="https://www.instagram.com" target="_blank" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="https://www.messenger.com" target="_blank" aria-label="Messenger"><i class="fab fa-facebook-messenger"></i></a>
                </div>
            </div>
            <div class="newsletter">
                <h3>Newsletter</h3>
                <form id="newsletterForm" novalidate>
                    <input type="hidden" name="_subject" value="New newsletter subscription">
                    <input type="hidden" name="_template" value="table">
                    <input type="email" name="email" placeholder="Your email">
                    <button type="submit">Subscribe</button>
                </form>
            </div>
        </footer>
    </div>

    <script type="module">
        import { products } from '../js/products.js';
        
        // Initialize wishlist
        let wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
        
        document.addEventListener('DOMContentLoaded', function() {
            const productGrid = document.querySelector('.product-grid');
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            // Display all products by default
            displayProducts(products);
            
            // Add event listeners to filter buttons
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Update active button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    // Filter products
                    const filter = button.dataset.filter;
                    const filteredProducts = filter === 'all' 
                        ? products 
                        : products.filter(product => product.type === filter);
                    
                    displayProducts(filteredProducts);
                });
            });
            
            function displayProducts(productsToShow) {
                productGrid.innerHTML = productsToShow.map(product => `
                    <div class="product-card" data-product-id="${product.id}">
                        <div class="product-badge">New</div>
                        <div class="product-image">
                            <img src="${product.image}" alt="${product.name}" loading="lazy">
                        </div>
                        <div class="product-info">
                            <h3 class="product-title">${product.name}</h3>
                            <div class="product-footer">
                                <div class="product-price">
                                    <span class="price-amount">€${product.price.toFixed(2)}</span>
                                </div>
                                <div class="product-actions">
                                    <button class="btn btn-customize" onclick="${product.id === 1 ? 'window.location.href=\'../Product pages/customize.html\'' : `customizeProduct(${product.id}, this)`}">
                                        <i class="fas fa-magic"></i>
                                        <span>${product.id === 1 ? 'Customize' : 'Customize'}</span>
                                    </button>
                                    <div class="action-buttons">
                                        
                                        <button class="btn btn-wishlist" data-product-id="${product.id}" onclick="event.stopPropagation(); toggleWishlist(this, ${product.id})" aria-label="Add to wishlist">
                                            <i class="${wishlist.includes(product.id) ? 'fas' : 'far'} fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
          
            
        });
    </script>
    <script src="../js/script.js"></script>
    <script src="../js/auth-utils.js"></script>
    <script src="../js/wishlist.js"></script>
    <script src="../js/cart.js"></script>
    <script type="module" src="../js/search-animated.js"></script>
    <script type="module" src="../js/subscribe.js"></script>
</body>
</html>