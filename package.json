{"name": "julia-magnet-website", "version": "1.0.0", "description": "Julia's Magnet Website with User Authentication", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js", "view-users": "node scripts/view-users.js", "user-stats": "node scripts/user-stats.js"}, "keywords": ["magnet", "website", "authentication", "express", "sqlite"], "author": "Julia Ma<PERSON>t Website", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "validator": "^13.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}