document.addEventListener('DOMContentLoaded', function() {
    // Create modal HTML for contact form
    const modalHTML = `
        <div class="modal-overlay" id="contactModal">
            <div class="modal">
                <div class="modal-header">
                    <h3>Message Status</h3>
                </div>
                <div class="modal-body" id="contactMessage">
                    <!-- Message will be inserted here -->
                </div>
                <div class="modal-footer">
                    <button class="modal-btn modal-btn-close">Close</button>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to the body if it doesn't already exist
    if (!document.getElementById('contactModal')) {
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    // Get modal elements
    const modal = document.getElementById('contactModal');
    const modalMessage = document.getElementById('contactMessage');
    const modalCloseBtn = modal.querySelector('.modal-btn-close');
    
    // Close modal function
    function closeModal() {
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
    }
    
    // Show modal function
    function showModal(message, type = 'success') {
        modalMessage.textContent = message;
        const modalElement = modal.querySelector('.modal');
        modalElement.className = 'modal';
        
        // Add the appropriate class based on type
        if (type === 'error' || type === 'warning' || type === 'success') {
            modalElement.classList.add(type);
        } else {
            modalElement.classList.add('success');
        }
        
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Focus the close button for better accessibility
        setTimeout(() => {
            modalCloseBtn.focus();
        }, 100);
    }
    
    // Close modal when clicking the close button
    modalCloseBtn.addEventListener('click', closeModal);
    
    // Close modal when clicking outside the modal content
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('active')) {
            closeModal();
        }
    });

    /**
     * Validates the format of an email address.
     * @param {string} emailValue The email string to validate.
     * @param {HTMLInputElement} emailInput The input element for focusing and styling on error.
     * @returns {boolean} True if the email format is valid, false otherwise.
     */
    function isEmailFormatValid(emailValue, emailInput) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailValue)) {
            showModal('Please enter a valid email address (e.g., <EMAIL>).', 'error');
            emailInput.classList.add('input-error');
            emailInput.focus();
            return false;
        }
        return true;
    }

    /**
     * Validates the domain of an email address against an allowed list.
     * @param {string} emailValue The email string to validate.
     * @param {HTMLInputElement} emailInput The input element for focusing and styling on error.
     * @returns {boolean} True if the email domain is allowed, false otherwise.
     */
    function isEmailDomainValid(emailValue, emailInput) {
        const allowedDomains = ['gmail.com', 'icloud.com'];
        const domain = emailValue.split('@')[1]?.toLowerCase();
        if (!domain || !allowedDomains.includes(domain)) {
            showModal('Please use a @gmail.com or @icloud.com email address.', 'warning');
            emailInput.classList.add('input-error');
            emailInput.focus();
            return false;
        }
        return true;
    }

    // Handle contact form submission
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        // Prevent default HTML5 validation
        contactForm.setAttribute('novalidate', 'novalidate');
        
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const submitButton = contactForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;

            const nameInput = contactForm.querySelector('#name');
            const emailInput = contactForm.querySelector('#email');
            const subjectInput = contactForm.querySelector('#subject');
            const messageInput = contactForm.querySelector('#message');
            const fields = [nameInput, emailInput, subjectInput, messageInput];

            // Clear previous errors
            fields.forEach(field => field.classList.remove('input-error'));

            const name = nameInput.value.trim();
            const email = emailInput.value.trim();
            const subject = subjectInput.value.trim();
            const message = messageInput.value.trim();

            // Validate required fields
            if (!name || !email || !subject || !message) {
                showModal('Please fill out all required fields.', 'error');
                let firstInvalidField = null;

                if (!name) { nameInput.classList.add('input-error'); if (!firstInvalidField) firstInvalidField = nameInput; }
                if (!email) { emailInput.classList.add('input-error'); if (!firstInvalidField) firstInvalidField = emailInput; }
                if (!subject) { subjectInput.classList.add('input-error'); if (!firstInvalidField) firstInvalidField = subjectInput; }
                if (!message) { messageInput.classList.add('input-error'); if (!firstInvalidField) firstInvalidField = messageInput; }
                
                if (firstInvalidField) firstInvalidField.focus();
                return;
            }
            
            // Validate email format and domain
            if (!isEmailFormatValid(email, emailInput) || !isEmailDomainValid(email, emailInput)) {
                return;
            }
            
            // Disable submit button and show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';

            const formData = new FormData(contactForm);

            // Send form data to FormSubmit
            fetch('https://formsubmit.co/ajax/<EMAIL>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Contact form submitted successfully:', data);
                showModal('Thank you for your message! I will get back to you soon.', 'success');
                contactForm.reset();
            })
            .catch(error => {
                console.error('Error submitting contact form:', error);
                showModal('There was an error sending your message. Please try again later.', 'error');
            })
            .finally(() => {
                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.textContent = originalButtonText;
            });
        });

        // Remove error state when typing in any contact form field
        contactForm.addEventListener('input', function(e) {
            if (e.target.matches('input, textarea')) {
                e.target.classList.remove('input-error');
            }
        });
    }
});
