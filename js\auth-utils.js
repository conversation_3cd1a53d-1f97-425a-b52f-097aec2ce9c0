// Authentication Utilities for other pages

class AuthUtils {
    constructor() {
        this.currentUser = this.loadCurrentUser();
        this.initializeAuthUI();
    }

    // Load current user from localStorage
    loadCurrentUser() {
        const user = localStorage.getItem('withLoveCurrentUser');
        return user ? JSON.parse(user) : null;
    }

    // Initialize authentication UI elements
    initializeAuthUI() {
        // Update navigation based on login status
        this.updateNavigation();
        
        // Add logout functionality if user is logged in
        if (this.currentUser) {
            this.addLogoutButton();
        }
    }

    // Update navigation to show user status
    updateNavigation() {
        const navMenu = document.querySelector('.nav-menu ul');
        if (!navMenu) return;

        // Find the login link
        const loginLink = navMenu.querySelector('a[href*="login.html"]');
        if (!loginLink) return;

        if (this.currentUser) {
            // User is logged in - replace login link with user menu
            const loginListItem = loginLink.parentElement;
            loginListItem.innerHTML = `
                <div class="user-menu">
                    <span class="user-greeting">Hi, ${this.currentUser.name.split(' ')[0]}!</span>
                    <button class="logout-btn" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            `;

            // Add logout functionality
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', () => this.logout());
            }
        }
    }

    // Add logout button functionality
    addLogoutButton() {
        // Add CSS for user menu if not already present
        if (!document.getElementById('auth-utils-styles')) {
            const style = document.createElement('style');
            style.id = 'auth-utils-styles';
            style.textContent = `
                .user-menu {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    padding: 0.5rem;
                }

                .user-greeting {
                    color: #ff6b8b;
                    font-weight: 600;
                    font-size: 0.9rem;
                }

                .logout-btn {
                    background: linear-gradient(135deg, #ff6b8b, #ff8fab);
                    color: white;
                    border: none;
                    padding: 0.4rem 0.8rem;
                    border-radius: 8px;
                    font-size: 0.8rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 0.3rem;
                }

                .logout-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(255, 107, 129, 0.3);
                }

                @media (max-width: 768px) {
                    .user-menu {
                        flex-direction: column;
                        gap: 0.5rem;
                        text-align: center;
                    }
                    
                    .logout-btn {
                        font-size: 0.9rem;
                        padding: 0.5rem 1rem;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Logout function
    logout() {
        // Show confirmation
        if (confirm('Are you sure you want to logout?')) {
            localStorage.removeItem('withLoveCurrentUser');
            
            // Show success message if possible
            this.showMessage('You have been logged out successfully', 'success');
            
            // Redirect to login page after short delay
            setTimeout(() => {
                window.location.href = '/Html pages/login.html';
            }, 1500);
        }
    }

    // Show message (simple version for pages without message container)
    showMessage(text, type) {
        // Create temporary message element
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        `;

        if (type === 'success') {
            message.style.background = 'linear-gradient(135deg, #2ed573, #7bed9f)';
        } else {
            message.style.background = 'linear-gradient(135deg, #ff4757, #ff6b8b)';
        }

        message.textContent = text;
        document.body.appendChild(message);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    // Get current user (utility function)
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is logged in (utility function)
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Refresh auth status (call this if user data might have changed)
    refresh() {
        this.currentUser = this.loadCurrentUser();
        this.updateNavigation();
    }
}

// Initialize auth utils when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if not on login page
    if (!window.location.pathname.includes('login.html')) {
        window.authUtils = new AuthUtils();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthUtils;
}
