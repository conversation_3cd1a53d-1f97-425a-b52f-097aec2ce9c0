// Authentication Utilities for other pages

class AuthUtils {
    constructor() {
        this.currentUser = this.loadCurrentUser();
        this.initializeAuthUI();
    }

    // Load current user from localStorage
    loadCurrentUser() {
        const user = localStorage.getItem('withLoveCurrentUser');
        return user ? JSON.parse(user) : null;
    }

    // Initialize authentication UI elements
    initializeAuthUI() {
        // Update navigation based on login status
        this.updateNavigation();
        
        // Add logout functionality if user is logged in
        if (this.currentUser) {
            this.addLogoutButton();
        }
    }

    // Update navigation to show user status
    updateNavigation() {
        const navMenu = document.querySelector('.nav-menu ul');
        if (!navMenu) return;

        // Find the login link
        const loginLink = navMenu.querySelector('a[href*="login.html"]');
        if (!loginLink) return;

        if (this.currentUser) {
            // User is logged in - replace login link with expandable user menu
            const loginListItem = loginLink.parentElement;
            loginListItem.innerHTML = `
                <div class="user-menu-container">
                    <a href="#" class="user-toggle" id="userToggle">
                        <i class="fas fa-user-circle"></i>
                        <span class="user-name">${this.currentUser.name.split(' ')[0]}</span>
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </a>
                </div>
            `;

            // Add user menu items after the user toggle
            const userMenuItems = `
                <li class="user-menu-item user-info-item" id="userInfoItem" style="display: none;">
                    <div class="user-info-content">
                        <div class="user-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="user-details">
                            <span class="user-full-name">${this.currentUser.name}</span>
                            <span class="user-email">${this.currentUser.email}</span>
                        </div>
                    </div>
                </li>
                <li class="user-menu-item" id="accountItem" style="display: none;">
                    <a href="#" class="user-menu-link" id="accountBtn">
                        <i class="fas fa-user-cog"></i>
                        <span>My Account</span>
                    </a>
                </li>
                <li class="user-menu-item" id="ordersItem" style="display: none;">
                    <a href="#" class="user-menu-link" id="ordersBtn">
                        <i class="fas fa-shopping-bag"></i>
                        <span>My Orders</span>
                    </a>
                </li>
                <li class="user-menu-item" id="logoutItem" style="display: none;">
                    <a href="#" class="user-menu-link logout-link" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </li>
            `;

            // Insert user menu items after the current list item
            loginListItem.insertAdjacentHTML('afterend', userMenuItems);

            // Add event listeners
            this.setupUserDropdown();
        }
    }

    // Setup user dropdown functionality
    setupUserDropdown() {
        const userToggle = document.getElementById('userToggle');
        const userInfoItem = document.getElementById('userInfoItem');
        const accountItem = document.getElementById('accountItem');
        const ordersItem = document.getElementById('ordersItem');
        const logoutItem = document.getElementById('logoutItem');
        const logoutBtn = document.getElementById('logoutBtn');
        const accountBtn = document.getElementById('accountBtn');
        const ordersBtn = document.getElementById('ordersBtn');

        const userMenuItems = [userInfoItem, accountItem, ordersItem, logoutItem];
        let isExpanded = false;

        if (userToggle) {
            // Toggle user menu items on click
            userToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                isExpanded = !isExpanded;
                userToggle.classList.toggle('active', isExpanded);

                // Show/hide user menu items with animation
                userMenuItems.forEach((item, index) => {
                    if (item) {
                        if (isExpanded) {
                            setTimeout(() => {
                                item.style.display = 'block';
                                // Trigger animation
                                setTimeout(() => {
                                    item.classList.add('visible');
                                }, 10);
                            }, index * 50); // Stagger the animation
                        } else {
                            item.classList.remove('visible');
                            setTimeout(() => {
                                item.style.display = 'none';
                            }, 200);
                        }
                    }
                });
            });

            // Close user menu when menu is closed (mobile)
            const navMenu = document.getElementById('navMenu');
            if (navMenu) {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            if (!navMenu.classList.contains('active') && isExpanded) {
                                // Close user menu when main menu closes
                                isExpanded = false;
                                userToggle.classList.remove('active');
                                userMenuItems.forEach((item) => {
                                    if (item) {
                                        item.classList.remove('visible');
                                        item.style.display = 'none';
                                    }
                                });
                            }
                        }
                    });
                });
                observer.observe(navMenu, { attributes: true });
            }
        }

        // Add logout functionality
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }

        // Add placeholder functionality for account and orders
        if (accountBtn) {
            accountBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showMessage('Account management coming soon!', 'info');
            });
        }

        if (ordersBtn) {
            ordersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showMessage('Order history coming soon!', 'info');
            });
        }
    }

    // Add user dropdown styles
    addLogoutButton() {
        // Add CSS for user dropdown if not already present
        if (!document.getElementById('auth-utils-styles')) {
            const style = document.createElement('style');
            style.id = 'auth-utils-styles';
            style.textContent = `
                /* User Menu Container */
                .user-menu-container {
                    display: block;
                }

                /* User Toggle Button */
                .user-toggle {
                    color: #a67f83 !important;
                    text-decoration: none !important;
                    font-size: 18px !important;
                    font-weight: 500 !important;
                    display: flex !important;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 8px 15px !important;
                    border-radius: 8px !important;
                    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
                    position: relative;
                    transform: translateY(0);
                    background: transparent !important;
                    margin: 2px 0;
                    cursor: pointer;
                    justify-content: center;
                }

                .user-toggle:hover,
                .user-toggle.active {
                    color: #ffffff !important;
                    background-color: #f0a1a1 !important;
                    transform: translateY(-3px) !important;
                    box-shadow: 0 4px 12px rgba(166, 127, 131, 0.3) !important;
                }

                .user-toggle::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    width: 0;
                    height: 2px;
                    background: #fff;
                    transition: all 0.3s ease;
                    transform: translateX(-50%);
                }

                .user-toggle:hover::after,
                .user-toggle.active::after {
                    width: 70%;
                }

                .user-name {
                    font-size: inherit;
                }

                .dropdown-arrow {
                    font-size: 12px;
                    transition: transform 0.3s ease;
                }

                .user-toggle.active .dropdown-arrow {
                    transform: rotate(180deg);
                }

                /* User Menu Items */
                .user-menu-item {
                    margin: 0;
                    padding: 0;
                    opacity: 0;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                }

                .user-menu-item.visible {
                    opacity: 1;
                    transform: translateY(0);
                }

                /* User Info Item */
                .user-info-item {
                    background: rgba(240, 161, 161, 0.1);
                    border-radius: 8px;
                    margin: 5px 0;
                    padding: 8px 0;
                }

                .user-info-content {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0 15px;
                    justify-content: center;
                }

                .user-avatar {
                    font-size: 1.5rem;
                    color: #a67f83;
                }

                .user-details {
                    display: flex;
                    flex-direction: column;
                    gap: 0.2rem;
                    text-align: left;
                }

                .user-full-name {
                    font-weight: 600;
                    color: #a67f83;
                    font-size: 0.9rem;
                }

                .user-email {
                    font-size: 0.8rem;
                    color: #8c6b6e;
                }

                /* User Menu Links */
                .user-menu-link {
                    color: #a67f83 !important;
                    text-decoration: none !important;
                    font-size: 16px !important;
                    font-weight: 500 !important;
                    display: flex !important;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 6px 15px !important;
                    border-radius: 8px !important;
                    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
                    position: relative;
                    transform: translateY(0);
                    background: transparent !important;
                    margin: 2px 0;
                    justify-content: center;
                }

                .user-menu-link:hover {
                    color: #ffffff !important;
                    background-color: rgba(240, 161, 161, 0.7) !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 2px 8px rgba(166, 127, 131, 0.2) !important;
                }

                .user-menu-link::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    width: 0;
                    height: 2px;
                    background: #fff;
                    transition: all 0.3s ease;
                    transform: translateX(-50%);
                }

                .user-menu-link:hover::after {
                    width: 60%;
                }

                .user-menu-link i {
                    width: 16px;
                    text-align: center;
                    font-size: 0.9rem;
                }

                /* Logout Link Special Styling */
                .logout-link {
                    color: #d63031 !important;
                }

                .logout-link:hover {
                    background-color: rgba(214, 48, 49, 0.1) !important;
                    color: #d63031 !important;
                }

                /* Mobile Responsive Styles */
                @media (max-width: 768px) {
                    .user-toggle {
                        justify-content: center;
                        width: 100%;
                        font-size: 18px !important;
                    }

                    .user-info-content {
                        justify-content: center;
                        text-align: center;
                        flex-direction: column;
                        gap: 0.5rem;
                    }

                    .user-details {
                        text-align: center;
                    }

                    .user-menu-link {
                        justify-content: center;
                        padding: 8px 15px !important;
                        font-size: 18px !important;
                    }

                    .user-menu-link:hover {
                        transform: translateY(-3px) !important;
                        background-color: #f0a1a1 !important;
                    }
                }

                /* Desktop specific adjustments */
                @media (min-width: 769px) {
                    .user-info-content {
                        justify-content: flex-start;
                    }

                    .user-details {
                        text-align: left;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Logout function
    logout() {
        // Show confirmation
        if (confirm('Are you sure you want to logout?')) {
            localStorage.removeItem('withLoveCurrentUser');
            
            // Show success message if possible
            this.showMessage('You have been logged out successfully', 'success');
            
            // Redirect to login page after short delay
            setTimeout(() => {
                window.location.href = '/Html pages/login.html';
            }, 1500);
        }
    }

    // Show message (simple version for pages without message container)
    showMessage(text, type) {
        // Create temporary message element
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        `;

        if (type === 'success') {
            message.style.background = 'linear-gradient(135deg, #2ed573, #7bed9f)';
        } else if (type === 'info') {
            message.style.background = 'linear-gradient(135deg, #3742fa, #5352ed)';
        } else {
            message.style.background = 'linear-gradient(135deg, #ff4757, #ff6b8b)';
        }

        message.textContent = text;
        document.body.appendChild(message);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    // Get current user (utility function)
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is logged in (utility function)
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Refresh auth status (call this if user data might have changed)
    refresh() {
        this.currentUser = this.loadCurrentUser();
        this.updateNavigation();
    }
}

// Initialize auth utils when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if not on login page
    if (!window.location.pathname.includes('login.html')) {
        window.authUtils = new AuthUtils();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthUtils;
}
