// Authentication Utilities for other pages

class AuthUtils {
    constructor() {
        this.currentUser = this.loadCurrentUser();
        this.initializeAuthUI();
    }

    // Load current user from localStorage
    loadCurrentUser() {
        const user = localStorage.getItem('withLoveCurrentUser');
        return user ? JSON.parse(user) : null;
    }

    // Initialize authentication UI elements
    initializeAuthUI() {
        // Update navigation based on login status
        this.updateNavigation();
        
        // Add logout functionality if user is logged in
        if (this.currentUser) {
            this.addLogoutButton();
        }
    }

    // Update navigation to show user status
    updateNavigation() {
        const navMenu = document.querySelector('.nav-menu ul');
        if (!navMenu) return;

        // Find the login link
        const loginLink = navMenu.querySelector('a[href*="login.html"]');
        if (!loginLink) return;

        if (this.currentUser) {
            // User is logged in - replace login link with user dropdown
            const loginListItem = loginLink.parentElement;
            loginListItem.innerHTML = `
                <div class="user-dropdown">
                    <a href="#" class="user-toggle" id="userToggle">
                        <i class="fas fa-user-circle"></i>
                        <span class="user-name">${this.currentUser.name.split(' ')[0]}</span>
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </a>
                    <div class="user-dropdown-menu" id="userDropdownMenu">
                        <div class="user-info">
                            <div class="user-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="user-details">
                                <span class="user-full-name">${this.currentUser.name}</span>
                                <span class="user-email">${this.currentUser.email}</span>
                            </div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item" id="accountBtn">
                            <i class="fas fa-user-cog"></i>
                            <span>My Account</span>
                        </a>
                        <a href="#" class="dropdown-item" id="ordersBtn">
                            <i class="fas fa-shopping-bag"></i>
                            <span>My Orders</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item logout-item" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    </div>
                </div>
            `;

            // Add event listeners
            this.setupUserDropdown();
        }
    }

    // Setup user dropdown functionality
    setupUserDropdown() {
        const userToggle = document.getElementById('userToggle');
        const userDropdownMenu = document.getElementById('userDropdownMenu');
        const logoutBtn = document.getElementById('logoutBtn');
        const accountBtn = document.getElementById('accountBtn');
        const ordersBtn = document.getElementById('ordersBtn');

        if (userToggle && userDropdownMenu) {
            // Toggle dropdown on click
            userToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                userDropdownMenu.classList.toggle('active');
                userToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!userToggle.contains(e.target) && !userDropdownMenu.contains(e.target)) {
                    userDropdownMenu.classList.remove('active');
                    userToggle.classList.remove('active');
                }
            });

            // Close dropdown when menu is closed (mobile)
            const navMenu = document.getElementById('navMenu');
            if (navMenu) {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            if (!navMenu.classList.contains('active')) {
                                userDropdownMenu.classList.remove('active');
                                userToggle.classList.remove('active');
                            }
                        }
                    });
                });
                observer.observe(navMenu, { attributes: true });
            }
        }

        // Add logout functionality
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }

        // Add placeholder functionality for account and orders
        if (accountBtn) {
            accountBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showMessage('Account management coming soon!', 'info');
            });
        }

        if (ordersBtn) {
            ordersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showMessage('Order history coming soon!', 'info');
            });
        }
    }

    // Add user dropdown styles
    addLogoutButton() {
        // Add CSS for user dropdown if not already present
        if (!document.getElementById('auth-utils-styles')) {
            const style = document.createElement('style');
            style.id = 'auth-utils-styles';
            style.textContent = `
                /* User Dropdown Styles */
                .user-dropdown {
                    position: relative;
                    display: block;
                }

                .user-toggle {
                    color: #a67f83 !important;
                    text-decoration: none !important;
                    font-size: 18px !important;
                    font-weight: 500 !important;
                    display: flex !important;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 8px 15px !important;
                    border-radius: 8px !important;
                    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
                    position: relative;
                    transform: translateY(0);
                    background: transparent !important;
                    margin: 2px 0;
                    cursor: pointer;
                }

                .user-toggle:hover,
                .user-toggle.active {
                    color: #ffffff !important;
                    background-color: #f0a1a1 !important;
                    transform: translateY(-3px) !important;
                    box-shadow: 0 4px 12px rgba(166, 127, 131, 0.3) !important;
                }

                .user-toggle::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    width: 0;
                    height: 2px;
                    background: #fff;
                    transition: all 0.3s ease;
                    transform: translateX(-50%);
                }

                .user-toggle:hover::after,
                .user-toggle.active::after {
                    width: 70%;
                }

                .user-name {
                    font-size: inherit;
                }

                .dropdown-arrow {
                    font-size: 12px;
                    transition: transform 0.3s ease;
                }

                .user-toggle.active .dropdown-arrow {
                    transform: rotate(180deg);
                }

                /* Dropdown Menu */
                .user-dropdown-menu {
                    position: absolute;
                    top: 100%;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #f0d9d9;
                    border-radius: 12px;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    padding: 1rem;
                    min-width: 220px;
                    z-index: 1002;
                    opacity: 0;
                    visibility: hidden;
                    transform: translateX(-50%) translateY(-10px);
                    transition: all 0.3s ease;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    backdrop-filter: blur(10px);
                    -webkit-backdrop-filter: blur(10px);
                }

                .user-dropdown-menu.active {
                    opacity: 1;
                    visibility: visible;
                    transform: translateX(-50%) translateY(0);
                }

                .user-info {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.5rem;
                }

                .user-avatar {
                    font-size: 2rem;
                    color: #a67f83;
                }

                .user-details {
                    display: flex;
                    flex-direction: column;
                    gap: 0.2rem;
                }

                .user-full-name {
                    font-weight: 600;
                    color: #a67f83;
                    font-size: 0.9rem;
                }

                .user-email {
                    font-size: 0.8rem;
                    color: #8c6b6e;
                }

                .dropdown-divider {
                    height: 1px;
                    background: rgba(166, 127, 131, 0.2);
                    margin: 0.75rem 0;
                }

                .dropdown-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.6rem 0.5rem;
                    color: #a67f83;
                    text-decoration: none;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                    font-size: 0.9rem;
                    margin: 0.2rem 0;
                }

                .dropdown-item:hover {
                    background: rgba(240, 161, 161, 0.3);
                    color: #8c6b6e;
                    transform: translateX(3px);
                }

                .dropdown-item i {
                    width: 16px;
                    text-align: center;
                    font-size: 0.9rem;
                }

                .logout-item {
                    color: #d63031;
                }

                .logout-item:hover {
                    background: rgba(214, 48, 49, 0.1);
                    color: #d63031;
                }

                /* Mobile Responsive Styles */
                @media (max-width: 768px) {
                    .user-dropdown-menu {
                        position: static;
                        transform: none;
                        margin-top: 0.5rem;
                        width: 100%;
                        min-width: auto;
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    }

                    .user-dropdown-menu.active {
                        transform: none;
                    }

                    .user-toggle {
                        justify-content: center;
                        width: 100%;
                    }

                    .user-info {
                        justify-content: center;
                        text-align: center;
                    }

                    .dropdown-item {
                        justify-content: center;
                        padding: 0.8rem 0.5rem;
                        font-size: 1rem;
                    }

                    .dropdown-item:hover {
                        transform: none;
                        background: rgba(240, 161, 161, 0.5);
                    }
                }

                /* Desktop specific adjustments */
                @media (min-width: 769px) {
                    .user-dropdown-menu {
                        right: 0;
                        left: auto;
                        transform: translateY(-10px);
                    }

                    .user-dropdown-menu.active {
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Logout function
    logout() {
        // Show confirmation
        if (confirm('Are you sure you want to logout?')) {
            localStorage.removeItem('withLoveCurrentUser');
            
            // Show success message if possible
            this.showMessage('You have been logged out successfully', 'success');
            
            // Redirect to login page after short delay
            setTimeout(() => {
                window.location.href = '/Html pages/login.html';
            }, 1500);
        }
    }

    // Show message (simple version for pages without message container)
    showMessage(text, type) {
        // Create temporary message element
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        `;

        if (type === 'success') {
            message.style.background = 'linear-gradient(135deg, #2ed573, #7bed9f)';
        } else if (type === 'info') {
            message.style.background = 'linear-gradient(135deg, #3742fa, #5352ed)';
        } else {
            message.style.background = 'linear-gradient(135deg, #ff4757, #ff6b8b)';
        }

        message.textContent = text;
        document.body.appendChild(message);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    // Get current user (utility function)
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is logged in (utility function)
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Refresh auth status (call this if user data might have changed)
    refresh() {
        this.currentUser = this.loadCurrentUser();
        this.updateNavigation();
    }
}

// Initialize auth utils when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if not on login page
    if (!window.location.pathname.includes('login.html')) {
        window.authUtils = new AuthUtils();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthUtils;
}
