# 🧲 <PERSON>'s Magnet Website

A beautiful, custom magnet making website where <PERSON> can promote and sell her handmade magnets online.

![Website Preview](images/logo.png)

## ✨ Features

- 🛍️ **E-commerce Store** - Complete shopping cart and product catalog
- 🎨 **Product Customization** - Personalized magnets with custom text and designs
- 🔐 **User Authentication** - Secure login system with database storage
- 📱 **Responsive Design** - Beautiful interface that works on all devices
- 💖 **Heart-themed Design** - Elegant pink/rose color scheme
- 👥 **Admin Panel** - Easy user management and analytics

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Initialize Database
```bash
npm run init-db
```

### 3. Start the Server
```bash
npm start
```

### 4. Access Your Website
- **Main Website**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin/users
- **Login Page**: http://localhost:3000/Html pages/login.html

## 📁 Project Structure

```
julia-magnet-website/
├── 📄 Html pages/           # Website pages (index, about, shop, etc.)
├── 🎨 Style/               # CSS stylesheets
├── ⚡ js/                  # JavaScript functionality
├── 🖼️ images/              # Product images and assets
├── 🗄️ database/            # SQLite database
├── 🔧 scripts/             # Utility and management scripts
├── 📚 docs/                # Documentation and guides
├── 📊 data/                # JSON data files
├── 📦 archive/             # Archived React components and old files
├── 🌐 server.js            # Main Express server
├── 📦 package.json         # Dependencies and scripts
├── 🔧 .env                 # Environment configuration
└── 🚀 start.bat/start.sh   # Easy startup scripts
```

## 🛠️ Available Scripts

- `npm start` - Start the production server
- `npm run dev` - Start development server with auto-restart
- `npm run init-db` - Initialize the database
- `npm run view-users` - View all registered users
- `npm run user-stats` - Display user statistics

## 🔐 Test Account

A test account is automatically created:
- **Email**: <EMAIL>
- **Password**: password123

## 📖 Documentation

- 📋 [Database Setup Guide](docs/README-DATABASE-SETUP.md)
- 👥 [User Management Guide](docs/USER-MANAGEMENT-GUIDE.md)
- 📝 [Project Information](docs/project-info.md)

## 🛡️ Security Features

- ✅ Password hashing with bcrypt
- ✅ JWT token authentication
- ✅ Rate limiting protection
- ✅ SQL injection prevention
- ✅ Input validation and sanitization

## 🎨 Product Categories

- 👨 **Father's Day Specials** - Personalized gifts for dad
- 💒 **Wedding Collection** - Beautiful wedding keepsakes
- 🎂 **Birthday Magnets** - Custom birthday celebrations
- 🎨 **Custom Designs** - Fully personalized options

## 📞 Contact

**Julia Abela**
- 📧 Email: <EMAIL>
- 📱 Phone: +356 ********

## 🤝 Support

If you need help:

1. Check the [documentation](docs/) folder
2. Run `npm run view-users` to check user data
3. Visit the admin panel at http://localhost:3000/admin/users
4. Contact Julia for assistance

## 💝 Made with Love

This website was crafted with the same care and attention to detail that Julia puts into her handmade magnets. Every feature has been designed to provide the best experience for both Julia and her customers.

---

**🚀 Ready to start? Run `npm start` and visit http://localhost:3000**
