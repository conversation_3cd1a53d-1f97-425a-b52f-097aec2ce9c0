// c:/Users/<USER>/OneDrive/Desktop/julia magnet/shop.js
// Product data with templates and customization options
import { products } from './products.js';



document.addEventListener('DOMContentLoaded', function() {
    // Generate product grid
    const productGrid = document.querySelector('.product-grid');
    const isHomePage = document.querySelector('.hero .letter-image');

    if (productGrid) {
        // On the homepage, show only featured products. On the shop page, show first 3 products
        const productsToDisplay = isHomePage ? products.filter(p => p.featured) : products.slice(0, 3);

        productGrid.innerHTML = productsToDisplay.map(product => `
            <div class="product-card" data-product-id="${product.id}">
                <div class="product-image-container">
                    <img src="${product.image}" alt="${product.title}" class="product-main-image">
                    ${product.image_alt ? 
                        `<img src="${product.image_alt}" alt="${product.title} - Version 2" class="product-alt-image">` 
                        : ''}
                </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.title}</h3>
                    <p class="product-description">${product.description}</p>
                    <div class="product-footer">
                        <span class="product-price">$${product.price.toFixed(2)}</span>
                        <div class="button-group">
                            <button type="button" class="wishlist-btn" aria-label="Add to wishlist">
                                <i class="far fa-heart"></i>
                            </button>                            <a href="../Product pages/${product.type === 'calendar' ? 'customize-calendar.html' : 'customize-fathers-day.html'}" class="view-btn">Customize Now</a>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // Initialize image rotation for any product that has an alternate image
        productsToDisplay.forEach(product => {
            if (product.image_alt) {
                const productCard = document.querySelector(`[data-product-id="${product.id}"]`);
                if (productCard) {
                    const imageContainer = productCard.querySelector('.product-image');
                    if (imageContainer) {
                        setupImageRotation(imageContainer);
                    }
                }
            }
        });
    }

   
});
