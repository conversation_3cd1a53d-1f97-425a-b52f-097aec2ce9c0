// Wishlist functionality
let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

function toggleWishlist(button, productId) {
    const heartIcon = button.querySelector('i');
    const index = wishlist.indexOf(productId);
    
    // Toggle the active class for the button
    button.classList.toggle('active');
    
    // Toggle between far (outline) and fas (solid) for the heart icon
    if (index === -1) {
        // Add to wishlist
        wishlist.push(productId);
        heartIcon.classList.remove('far');
        heartIcon.classList.add('fas');
    } else {
        // Remove from wishlist
        wishlist.splice(index, 1);
        heartIcon.classList.remove('fas');
        heartIcon.classList.add('far');
    }
    
    // Save to localStorage
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
    
    // Update wishlist count
    updateWishlistCount();
}



