/* Loader Styles */
.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f9e8e8;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.heart-loader {
    position: relative;
    width: 100px;
    height: 100px;
    margin-bottom: 20px;
}

.heart {
    position: absolute;
    font-size: 60px;
    color: #ff6b8b;
    text-shadow: 0 0 10px rgba(255, 107, 129, 0.5);
    animation: pulse 1.5s infinite ease-in-out;
    transform-origin: center;
}

.shadow {
    width: 60px;
    height: 20px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    filter: blur(5px);
    animation: shadowPulse 1.5s infinite ease-in-out;
}

.loading-text {
    color: #a67f83;
    font-size: 18px;
    letter-spacing: 2px;
    margin-top: 20px;
    animation: fadeInOut 2s infinite ease-in-out;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

@keyframes shadowPulse {
    0%, 100% { transform: translateX(-50%) scale(0.8); opacity: 0.5; }
    50% { transform: translateX(-50%) scale(1); opacity: 0.8; }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Header Right Section */
.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Header Icons */
.header-icons {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-left: auto;
}

.header-icons a {
    color: #f7e7ce;
    font-size: 1.2rem;
    position: relative;
    transition: color 0.3s ease;
}

.header-icons a:hover {
    color: #ff6b8b;
}

.icon-badge {
    position: absolute;
    top: -8px;
    right: -10px;
    background-color: #ff6b8b;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    padding: 0 4px;
    transition: all 0.3s ease;
    transform-origin: center;
    z-index: 10;
}

/* Animation for count update */
@keyframes pop {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

.icon-badge.updated {
    animation: pop 0.3s ease-out;
    background-color: #ff4757;
    box-shadow: 0 0 0 2px rgba(255, 107, 129, 0.5);
}

/* Hover effect on parent */
.header-icons a:hover .icon-badge {
    transform: scale(1.1);
    background-color: #ff4757;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

/* Custom Scrollbar */

.features-grid {
    display: flex;
    gap: 25px;
    margin-top: 30px;
    overflow-x: auto;
    padding-bottom: 20px;
    scrollbar-width: thin;
    scrollbar-color: #d48a8a #f9e8e8;
}

/* For WebKit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: #f9e8e8;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #d48a8a;
    border-radius: 10px;
    border: 2px solid #f9e8e8;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: #c47a7a;
    box-shadow: 0 0 10px rgba(212, 138, 138, 0.5);
}

/* For Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: #d48a8a #f9e8e8;
}

/* For Edge */
@supports (-ms-ime-align: auto) {
    * {
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #d48a8a;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #c47a7a;
    }
}

body {
    background: linear-gradient(90deg,#f9c5c5 0%, #ecacac 50%, #f0a1a1 100%);
    background-attachment: fixed;
    color: #333;
    min-height: 100vh;
    margin: 0;
    scrollbar-width: thin;
    scrollbar-color: #ff9a9e #f9e8e8;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* ======================
   HEADER STYLES
   ====================== */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 2px solid #e0c9c9;
    margin-bottom: 20px;
    position: relative; /* Contain the absolutely positioned title */
    flex-wrap: wrap;
}

/* Logo */
.logo {
    flex: 0 0 auto;
    z-index: 10;
}

.logo img {
    width: 120px;  /* Increased from 70px */
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

/* Make logo slightly smaller on mobile */
@media (max-width: 768px) {
    .logo img {
        width: 70px;  /* Original size for mobile */
    }
}

/* Site Title */
.title {
    font-size: 36px;  /* Default size for desktop */
    font-weight: bold;
    color: #f7e7ce;
    text-align: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: auto;
    white-space: nowrap;
    margin: 0;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

/* Mobile styles */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 10px 0;
    }
    
    .title {
        position: static;
        transform: none;
        font-size: 24px;
        margin: 10px 0;
        max-width: 100%;
        order: 3;
        width: 100%;
        text-align: center;
    }
    
    .header-right {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        order: 4;
        padding: 10px 15px 0;
        border-top: 1px solid rgba(0,0,0,0.1);
        margin-top: 10px;
    }
    
    .search {
        flex: 1;
        max-width: 200px;
    }
    
    .logo {
        position: absolute;
        left: 15px;
        top: 10px;
        z-index: 10;
    }
    
    .logo img {
        width: 55px;
        height: auto;
        transition: transform 0.3s ease;
    }
    
    .logo img:active {
        transform: scale(0.95);
    }
    
    .hamburger {
        position: relative;
        width: 24px;
        height: 24px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin-left: 15px;
    }
    
    .header-icons {
        gap: 15px;
        margin-left: auto;
    }
    
    .header-icons a {
        font-size: 1.1rem;
    }
    
    .search-toggle {
        padding: 6px 10px;
        font-size: 0.9rem;
    }
}

/* Search Container */
.search {
    position: relative;
    display: flex;
    align-items: center;
    z-index: 100; /* Lower default z-index */
}

/* When menu is active, lower the search z-index to be under overlay */
.nav-menu.active + .header-right .search,
.nav-menu.active ~ .header-right .search {
    z-index: 500;
    position: relative;
}

.search-container {
    position: relative;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    opacity: 0;
    transition: transform 0.4s ease-out, opacity 0.3s ease-out, width 0.4s ease-out;
    width: 0;
    overflow: visible;
    will-change: transform, opacity, width;
    min-width: 0;
}

.search-container.active {
    transform: translateX(0);
    opacity: 1;
    width: 280px;
    min-width: 200px;
    z-index: 1000; /* Higher z-index when active */
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1), 
                opacity 0.3s ease-out, 
                width 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
}

/* Adjust search container width for mobile */
@media (max-width: 768px) {
    .search-container.active {
        width: 180px;
        min-width: 150px;
    }
    
    .search-input {
        padding: 8px 35px 8px 12px;
        font-size: 14px;
    }
    
    .search-toggle {
        padding: 8px 12px;
        font-size: 14px;
    }
}

.search-input-wrapper {
    position: relative;
    width: 100%;
}

.search-input {
    padding: 10px 40px 10px 15px;
    border: 2px solid #e0c9c9;
    border-radius: 25px;
    outline: none;
    width: 100%;
    background-color: #fff;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(166, 127, 131, 0.1);
}

.search-input:focus {
    border-color: #a67f83;
    box-shadow: 0 0 0 3px rgba(166, 127, 131, 0.2);
}

/* Search icon */
.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a67f83;
    cursor: pointer;
    z-index: 2;
    font-size: 16px;
    pointer-events: none; /* Allow clicks to pass through to the input */
}

/* Make sure the search icon container is positioned relatively */
.search-input-wrapper {
    position: relative;
    width: 100%;
    display: inline-block;
}

/* Adjust input padding to prevent text from going behind the icon */
.search-input {
    padding-right: 40px; /* Make room for the icon */
}

/* Search Suggestion Image */
.search-suggestion-item img.suggestion-image {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-right: 12px;
    border-radius: 4px;
}

.search-suggestion-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 120%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0c9c9;
    border-radius:20px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-suggestions.visible {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.search-suggestions::-webkit-scrollbar {
    width: 6px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: #f9f0f0;
    border-radius: 0 12px 12px 0;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: #d4b8b8;
    border-radius: 4px;
}

.search-suggestions::-webkit-scrollbar-thumb:hover {
    background: #c4a8a8;
}

.suggestion-item {
    padding: 12px 20px;
    color: #5a3e3e;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f8f0f0;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.suggestion-item:hover {
    background-color: #fff5f5;
}

.suggestion-item.keyword {
    color: #d48a8a;
}

.suggestion-content {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 12px;
}

.suggestion-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
}

.suggestion-details {
    flex: 1;
    min-width: 0;
}

.suggestion-title {
    font-weight: 500;
    color: #5a3e3e;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.suggestion-price {
    font-size: 0.85em;
    color: #d48a8a;
    font-weight: 600;
}

.suggestion-header {
    padding: 8px 20px;
    font-size: 0.85em;
    font-weight: 600;
    color: #a67f83;
    background-color: #f9f0f0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #f0e0e0;
}

.no-suggestions {
    padding: 15px 20px;
    color: #a67f83;
    text-align: center;
    font-style: italic;
    font-size: 0.9em;
}

.search-suggestion-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: #d48a8a;
    transform: scaleY(0);
    transition: transform 0.2s ease;
}

.search-suggestion-item:hover:before {
    transform: scaleY(1);
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

.search-suggestion-item:hover {
    background-color: #fdf8f8;
    color: #8c5e60;
    padding-left: 23px;
}

.search-suggestion-item img {
    width: 24px;
    height: 24px;
    object-fit: contain;
    border-radius: 4px;
    margin-right: 12px;
}

.suggestion-header {
    padding: 10px 20px;
    font-size: 12px;
    text-transform: uppercase;
    color: #a67f83;
    font-weight: 600;
    letter-spacing: 0.5px;
    background-color: #fcf8f8;
    border-bottom: 1px solid #f0e6e6;
}

.no-suggestions {
    padding: 15px 20px;
    color: #a67f83;
    font-style: italic;
    text-align: center;
    font-size: 14px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.search-icon {
    position: absolute;
    right: 10px;
    color: #a67f83;
    cursor: pointer;
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.search-toggle {
    background: none;
    border: none;
    color: #f7e7ce;
    font-size: 1rem;
    cursor: pointer;
    margin-left: 10px;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 600;
    position: relative;
}

.search-toggle:hover {
    transform: scale(1.16);
    color: #6d4f52;
}

.search-toggle.active {
    opacity: 0;
    pointer-events: none;
    width: 0;
    padding: 8px 0;
    margin: 0;
    overflow: hidden;
}


/* Hero section */
.hero {
    margin: 30px 0;
    padding: 50px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    position: relative;
}

.card {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

/* Products section */
.products {
    margin-bottom: 50px;
}

.products h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #f7e7ce;
    font-size: 36px;
}

.product-grid {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
}

.product-card {
    background: white;
    border-radius: 10px;
    padding: 15px 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 10px;
}

.product-image {
    width: 100%;
    height: 220px;
    background-color: #f0d9d9;
    margin: 0 auto 15px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a67f83;
    border-radius: 5px;
    overflow: visible;
    position: relative;
    background-color: transparent !important;
    background: none !important;
}

.product-image img {
    max-width: 95%;
    max-height: 95%;
    object-fit: contain;
    position: relative;
    z-index: 1;
}

.product-title {
    margin: 10px 0;
    color: #a67f83;
}

.view-btn {
    background-color: #a67f83;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.view-btn:hover {
    background-color: #db9097;
    transform: scale(1.05);
}

.view-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) inset;
}

.view-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.view-btn:focus:not(:active)::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

.product-card.simple {
    background: none;
    box-shadow: none;
    width: auto;
    padding: 0;
}

.simple-box {
    width: 100px;
    height: 100px;
    background-color: #f0d9d9;
    border-radius: 5px;
    margin-bottom: 10px;
}

.view-text {
    color: #a67f83;
    font-weight: bold;
}

.arrow {
    font-size: 24px;
    color: #a67f83;
    margin: 0 10px;
}

/* Hamburger Menu Styles */
.hamburger {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1000;
    position: relative;
}

.hamburger:hover .bar {
    background-color: #a67f83;
    transform: scale(1.16);

}

.bar {
    width: 100%;
    height: 3px;
    background-color: #f7e7ce;
    border-radius: 3px;
    transition: all 0.3s ease;
    transform-origin: center;
}

/* Navigation Menu Animation */
.nav-menu {
    position: fixed;
    top: 125px;
    right: -350px;  /* Moved further off-screen when inactive */
    width: 160px;
    height: 200px;
    background-color: #f0d9d9;
    padding: 20px;
    z-index: 1001; /* Increased to be above overlay */
    box-shadow: -2px 0 15px rgba(0, 0, 0, 0.1);
    margin-right: 20px;
    border-radius: 15px;
    opacity: 0;
    transform: translateX(20px);
    transition: 
        right 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55),
        opacity 0.4s ease,
        transform 0.4s ease;
}

.nav-menu.active {
    right: 30px;  /* Positioned 30px from the right when active */
    opacity: 1;
    transform: translateX(0);
    transition: 
        right 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
        opacity 0.3s ease 0.1s,
        transform 0.3s ease 0.1s;
}

.nav-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    text-align: center;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    color: #a67f83;
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    display: inline-block;
    padding: 8px 15px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    transform: translateY(0);
    background: transparent;
}

.nav-menu a:hover,
.nav-menu a:active,
.nav-menu a.active {
    color: #ffffff;
    background-color: #f0a1a1;
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(166, 127, 131, 0.3);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #fff;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-menu a:hover::after,
.nav-menu a:active::after,
.nav-menu a.active::after {
    width: 70%;
}

/* Close button styles */
.close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 28px;
    cursor: pointer;
    color: #a67f83;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #f7e7ce;
    transform: scale(1.16);

}

/* Hamburger to X Animation */
.hamburger.active .bar:nth-child(1) {
    transform: translateY(9px) rotate(135deg);
    background-color: #8c6b6e;
}

.hamburger.active .bar:nth-child(2) {
    opacity: 0;
    transform: scaleX(0);
}

.hamburger.active .bar:nth-child(3) {
    transform: translateY(-9px) rotate(-135deg);
    background-color: #8c6b6e;
}

/* Overlay Animation */
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000; /* Below menu but above other content */
    opacity: 0;
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: auto;
}

.overlay.active {
    display: block;
    opacity: 1;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* Footer */
footer {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 30px 0;
    border-top: 2px solid #e0c9c9;
    margin-top: auto;
}

.contact, .social, .newsletter {
    flex: 1;
    min-width: 200px;
    padding: 0 15px;
    margin-bottom: 20px;
    text-decoration: none;
}



footer h3 {
    color: #F7E7CE;
    margin-bottom: 15px;
    font-size: 18px;
}

footer a {
    color: #000000;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}



.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}



.social-icons a {
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    perspective: 1000px;
}

.social-icons i {
    font-size: 22px;
    color: #f7e7ce;
    background-color: #db9097;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    border: 2px solid transparent;
}

.social-icons i:hover {
    color: white;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}


.social-icons i.fa-facebook-f:hover {
    background: #3b5998;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Instagram specific styles */
.social-icons i.fa-instagram {
    background: #db9097; /* Original color when not hovered */
}

.social-icons i.fa-instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 20%, #dc2743 40%, #cc2366 60%, #bc1888 80%, #a67f83 100%);
    background-size: 200% 200%;
    animation: gradientShift 1.5s ease infinite;
    box-shadow: 0 0 20px rgba(220, 39, 67, 0.6);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.social-icons i.fa-facebook-messenger:hover {
    background: #0084ff;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.7);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.newsletter input {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #e0c9c9;
    border-radius: 4px;
}

.newsletter button {
    background-color: #a67f83;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.newsletter button:hover {
    background-color: #8c6a6e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(166, 127, 131, 0.4);
}

.newsletter button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(166, 127, 131, 0.4);
}

.newsletter button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.newsletter button:focus:not(:active)::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

/* Featured Products Container */
.featured-products-container {
    max-width: 1400px;
    margin: 0 auto 30px;
    padding: 0 15px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.product-image {
    width: 100%;
    height: 180px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-details {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.product-details h3 {
    margin: 0 0 10px;
    color: #5a3e3e;
    font-size: 1.4rem;
}

.product-details p {
    color: #7a6a6a;
    margin-bottom: 20px;
    line-height: 1.5;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #f0e6e6;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 600;
    color: #5a3e3e;
}

.view-btn {
    background-color: #a67f83;
    color: white;
    border: none;
    padding: 8px 25px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-btn:hover {
    background-color: #8c6a6e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(166, 127, 131, 0.4);
}

.view-btn:active {
    transform: translateY(0);
}

/* Success animation */
@keyframes success {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-message {
    display: none;
    color: #4caf50;
    font-weight: 600;
    margin-top: 10px;
    animation: success 0.6s ease-out;
}

    
    .hamburger {
        width: 22px;
        height: 18px;
    }
    
    .nav-menu {
        top: 120px;
        width: 65%;
        max-width: 240px;
        padding: 26px;
    }
    
    .nav-menu a {
        padding: 8px 10px;
        font-size: 14px;
        margin: 5px 0;
    }
    
    .search-container.active {
        width: 90%;
        max-width: 300px;
    }


/* Mobile menu styles */
@media (max-width: 768px) {
    .hamburger {
        width: 22px;
        height: 18px;
    }
    
  
    .nav-menu {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) translateY(-10px);
        width: 80%;
        height: 250px;
        background-color: #f0d9d9;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        text-align: center;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .nav-menu.active {
        opacity: 1;
        visibility: visible;
        transform: translate(-50%, -50%) translateY(0);
    }
    
    .nav-menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .nav-menu li {
        padding: 0 15px;
    }
    
    .nav-menu ul {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 0;
        margin: 0;
        list-style: none;
    }
    
    .nav-menu li {
        margin: 0;
        padding: 0;
    }
    
    .nav-menu a {
        color: #a67f83;
        text-decoration: none;
        font-size: 18px;
        font-weight: 500;
        display: inline-block;
        padding: 8px 15px;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        transform: translateY(0);
        background: transparent;
        margin: 2px 0;
    }
    
    .nav-menu a:hover {
        color: #ffffff;
    background-color: #f0a1a1;
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(166, 127, 131, 0.3);
    }
    
    .product-grid {
        flex-direction: column;
        align-items: center;
    }
    
    .arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }
    
    footer {
        flex-direction: column;
    }
}


/* From Uiverse.io by vishnu1100 */ 
.letter-image {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    -webkit-transform: translate(-50%, -50%) scale(1.5);
    -moz-transform: translate(-50%, -50%) scale(1.5);
    transform: translate(-50%, -50%) scale(1.5);
    cursor: pointer;
    transform-origin: center center;
}

/* Responsive scaling for envelope */
@media (max-width: 1024px) {
    .letter-image {
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@media (max-width: 768px) {
    .letter-image {
        transform: translate(-50%, -50%) scale(0.9);
    }
}

@media (max-width: 480px) {
    .letter-image {
        transform: translate(-50%, -50%) scale(0.7);
    }
}

.animated-mail {
    position: relative;
    height: 225px;
    width: 300px;
    -webkit-transition: 0.4s;
    -moz-transition: 0.4s;
    transition: 0.4s;
}

.heart {
    position: absolute;
    top: 65%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.5);
    z-index: 3;
    color: #e63946;
    font-size: 50px;
    transition: all 0.3s ease;
    pointer-events: none;
    text-shadow: 0 0 2px rgba(0,0,0,0.1);
}

.letter-image:hover .heart {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.4);
    transition: opacity 0.2s, transform 0.3s;
}

.animated-mail .body {
    position: absolute;
    bottom: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 150px 300px;
    border-color: transparent transparent #ffffff transparent;
    z-index: 2;
    
}

.animated-mail .top-fold {
    position: absolute;
    top: 75px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 75px 150px 0 150px;
    -webkit-transform-origin: 50% 0%;
    -webkit-transition: transform 0.4s 0.4s, z-index 0.2s 0.4s;
    -moz-transform-origin: 50% 0%;
    -moz-transition: transform 0.4s 0.4s, z-index 0.2s 0.4s;
    transform-origin: 50% 0%;
    transition: transform 0.4s 0.4s, z-index 0.2s 0.4s;
    border-color: #f8f8f8 transparent transparent transparent;
    z-index: 2;
}

.animated-mail .back-fold {
    position: absolute;
    bottom: 0;
    width: 300px;
    height: 150px;
    background: #ffffff;
    z-index: 0;
}

.animated-mail .left-fold {
    position: absolute;
    bottom: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 75px 0 75px 150px;
    border-color: transparent transparent transparent #ffffff;
    z-index: 2;
}



.animated-mail .letter {
    left: 30px;
    bottom: 0px;
    position: absolute;
    width: 240px;
    height: 90px;
    background: white;
    z-index: 1;
    overflow: hidden;
    -webkit-transition: 0.4s 0.2s;
    -moz-transition: 0.4s 0.2s;
    transition: 0.4s 0.2s;
    padding: 15px;
    box-sizing: border-box;
    transition: height 0.4s 0.2s, opacity 0.4s 0.2s;
}

.animated-mail .letter .letter-content {
    opacity: 0;
    transition: opacity 0.3s 0.4s;
    color: #333;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
}

.animated-mail .letter-content h3 {
    margin: 0 0 10px 0;
    color: #cf4a43;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
}

.animated-mail .letter-content p {
    margin: 5px 0;
}

.letter-image:hover .letter .letter-content {
    opacity: 1;
}

.letter-image:hover .letter {
    height: 270px;
    overflow-y: auto;
    opacity: 1;
}

.animated-mail .letter .letter-border {
    height: 15px;
    width: 100%;
    background: repeating-linear-gradient(
        -45deg,
        #cb5a5e,
        #cb5a5e 12px,
        transparent 12px,
        transparent 27px
    );
}

.animated-mail .letter .letter-title {
    margin-top: 15px;
    margin-left: 7.5px;
    height: 15px;
    width: 60%;
    background: #cb5a5e;
}

.animated-mail .letter .letter-context {
    margin-top: 15px;
    margin-left: 7.5px;
    height: 15px;
    width: 30%;
    background: #cb5a5e;
}

.animated-mail .letter .letter-stamp {
    margin-top: 45px;
    margin-left: 180px;
    border-radius: 100%;
    height: 45px;
    width: 45px;
    background: #cb5a5e;
    opacity: 0.3;
}



.letter-image:hover {
    .animated-mail {
        transform: translateY(75px);
        -webkit-transform: translateY(75px);
        -moz-transform: translateY(75px);
    }
  
    .animated-mail .top-fold {
        transition: transform 0.4s, z-index 0.2s;
        transform: rotateX(180deg);
        -webkit-transition: transform 0.4s, z-index 0.2s;
        -webkit-transform: rotateX(180deg);
        -moz-transition: transform 0.4s, z-index 0.2s;
        -moz-transform: rotateX(180deg);
        z-index: 0;
    }
  
    .animated-mail .letter {
        height: 270px;
    }
  
    .shadow {
        width: 375px;
    }
}

/* Floral Loader Styles */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f9e8e8 0%, #f5d0d0 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 1s ease-out, visibility 1s ease-out;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.floral-loader {
    position: relative;
    width: 150px;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.flower {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    animation: float 3s ease-in-out infinite;
}

.petals {
    position: absolute;
    width: 100%;
    height: 100%;
}

.petal {
    position: absolute;
    width: 40px;
    height: 20px;
    background: #ff9bb3;
    border-radius: 20px 20px 0 0;
    transform-origin: bottom center;
    opacity: 0.9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.petal:nth-child(1) { transform: rotate(0deg) translateY(-10px); }
.petal:nth-child(2) { transform: rotate(45deg) translateY(-10px); }
.petal:nth-child(3) { transform: rotate(90deg) translateY(-10px); }
.petal:nth-child(4) { transform: rotate(135deg) translateY(-10px); }
.petal:nth-child(5) { transform: rotate(180deg) translateY(-10px); }
.petal:nth-child(6) { transform: rotate(225deg) translateY(-10px); }
.petal:nth-child(7) { transform: rotate(270deg) translateY(-10px); }
.petal:nth-child(8) { transform: rotate(315deg) translateY(-10px); }

.center {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #fff5c0;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px rgba(255, 200, 200, 0.8);
    animation: pulse 2s infinite ease-in-out;
}

.stem {
    width: 6px;
    height: 80px;
    background: linear-gradient(to bottom, #8bc34a, #689f38);
    border-radius: 3px;
    position: relative;
}

.leaf {
    position: absolute;
    width: 30px;
    height: 15px;
    background: #8bc34a;
    border-radius: 15px 0;
    top: 40px;
    left: 50%;
    transform: rotate(45deg);
    transform-origin: left center;
    animation: leafWave 4s infinite ease-in-out;
}

.loading-text {
    margin-top: 30px;
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    color: #a67f83;
    letter-spacing: 2px;
    text-transform: uppercase;
    animation: fadeInOut 3s infinite ease-in-out;
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes leafWave {
    0%, 100% { transform: rotate(35deg); }
    50% { transform: rotate(55deg); }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}