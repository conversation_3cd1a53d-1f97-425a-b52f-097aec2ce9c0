const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'database', 'users.db');

// Check if database exists
const fs = require('fs');
if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found. Please run "npm run init-db" first.');
    process.exit(1);
}

// Connect to database
const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
        process.exit(1);
    } else {
        console.log('📈 Generating user statistics...\n');
        generateStats();
    }
});

async function generateStats() {
    try {
        // Basic user counts
        const userCounts = await query(`
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
                COUNT(CASE WHEN email_verified = 1 THEN 1 END) as verified_users,
                COUNT(CASE WHEN last_login IS NOT NULL THEN 1 END) as users_who_logged_in
            FROM users
        `);

        // Registration trends (last 30 days)
        const recentRegistrations = await query(`
            SELECT 
                DATE(registration_date) as reg_date,
                COUNT(*) as registrations
            FROM users 
            WHERE registration_date >= datetime('now', '-30 days')
            GROUP BY DATE(registration_date)
            ORDER BY reg_date DESC
            LIMIT 10
        `);

        // Login activity (last 30 days)
        const recentLogins = await query(`
            SELECT 
                DATE(last_login) as login_date,
                COUNT(*) as logins
            FROM users 
            WHERE last_login >= datetime('now', '-30 days')
            GROUP BY DATE(last_login)
            ORDER BY login_date DESC
            LIMIT 10
        `);

        // Most recent users
        const recentUsers = await query(`
            SELECT name, email, registration_date
            FROM users 
            ORDER BY registration_date DESC 
            LIMIT 5
        `);

        // Session statistics
        const sessionStats = await query(`
            SELECT 
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN expires_at > datetime('now') THEN 1 END) as active_sessions,
                COUNT(CASE WHEN created_at >= datetime('now', '-24 hours') THEN 1 END) as sessions_today
            FROM user_sessions
        `);

        // Display statistics
        console.log('═'.repeat(60));
        console.log('📊 USER STATISTICS DASHBOARD');
        console.log('═'.repeat(60));

        const stats = userCounts[0];
        console.log('\n👥 USER OVERVIEW:');
        console.log(`   Total Users: ${stats.total_users}`);
        console.log(`   Active Users: ${stats.active_users}`);
        console.log(`   Verified Emails: ${stats.verified_users}`);
        console.log(`   Users Who Logged In: ${stats.users_who_logged_in}`);

        if (stats.total_users > 0) {
            const activePercent = ((stats.active_users / stats.total_users) * 100).toFixed(1);
            const verifiedPercent = ((stats.verified_users / stats.total_users) * 100).toFixed(1);
            const loginPercent = ((stats.users_who_logged_in / stats.total_users) * 100).toFixed(1);
            
            console.log(`\n📈 PERCENTAGES:`);
            console.log(`   Active Rate: ${activePercent}%`);
            console.log(`   Verification Rate: ${verifiedPercent}%`);
            console.log(`   Login Rate: ${loginPercent}%`);
        }

        // Session stats
        const sessions = sessionStats[0];
        console.log(`\n🔐 SESSION STATISTICS:`);
        console.log(`   Total Sessions: ${sessions.total_sessions}`);
        console.log(`   Active Sessions: ${sessions.active_sessions}`);
        console.log(`   Sessions Today: ${sessions.sessions_today}`);

        // Recent registrations
        if (recentRegistrations.length > 0) {
            console.log(`\n📅 RECENT REGISTRATIONS (Last 30 days):`);
            recentRegistrations.forEach(reg => {
                console.log(`   ${reg.reg_date}: ${reg.registrations} user${reg.registrations > 1 ? 's' : ''}`);
            });
        }

        // Recent logins
        if (recentLogins.length > 0) {
            console.log(`\n🔑 RECENT LOGIN ACTIVITY (Last 30 days):`);
            recentLogins.forEach(login => {
                console.log(`   ${login.login_date}: ${login.logins} login${login.logins > 1 ? 's' : ''}`);
            });
        }

        // Recent users
        if (recentUsers.length > 0) {
            console.log(`\n🆕 NEWEST USERS:`);
            recentUsers.forEach((user, index) => {
                const date = new Date(user.registration_date).toLocaleDateString();
                console.log(`   ${index + 1}. ${user.name} (${user.email}) - ${date}`);
            });
        }

        console.log('\n═'.repeat(60));

    } catch (error) {
        console.error('Error generating statistics:', error);
    } finally {
        db.close();
    }
}

// Helper function to promisify database queries
function query(sql, params = []) {
    return new Promise((resolve, reject) => {
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}
