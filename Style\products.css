.products {
    margin-bottom: 50px;
}

.products h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #f7e7ce;
    font-size: 36px;
}
.product-grid {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
}

.product-card {
    background: white;
    border-radius: 10px;
    padding: 15px 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 10px;
}

.product-image {
    width: 100%;
    height: 220px;
    margin: 0 auto 15px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px; /* Added rounded corners */
    overflow: hidden; /* Changed from visible to contain the border radius */
    position: relative;
    background-color: transparent !important;
    background: none !important;
}

.product-image img {
    max-width: 95%;
    max-height: 95%;
    object-fit: contain;
    position: relative;
    z-index: 1;
    border-radius: 10px; /* Slightly smaller radius for the image */
}

.product-image-container {
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px; /* Added rounded corners */
    background-color: transparent !important;
    background: none !important;
}



.product-alt-image {
    opacity: 0;
}

.product-card {
    border-radius: 15px;
    overflow: hidden;
}


.product-title {
    margin: 10px 0;
    color: #a67f83;
}


/* Heartbeat Animation */
@keyframes heartbeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.3); }
    35% { transform: scale(1); }
    45% { transform: scale(1.3); }
    55% { transform: scale(1); }
}



/*btn styles*/
/* Wishlist Button Styles */
.btn-wishlist {
    background: none;
    border: none;
    color: #a67f83;
    cursor: pointer;
    font-size: 1.2em;
    padding: 8px;
    transition: all 0.3s ease;
    position: relative;
    outline: none;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.btn-wishlist:hover {
    transform: scale(1.1);
    background-color: rgba(255, 0, 0, 0.05);
}

/* Solid heart when active */
.btn-wishlist.active i.fa-heart {
    color: #ff0000 !important;
    animation: heartbeat 1s ease-in-out;
    font-weight: 900;
}

/* Outline heart when inactive */
.btn-wishlist i.fa-heart {
    transition: all 0.3s ease;
    display: inline-block;
    font-weight: 400;
}

/* Hover effect for outline heart */
.btn-wishlist:not(.active):hover i.fa-heart {
    color: #ff6b6b;
}

/* Pulsing effect for active state */
.btn-wishlist.active i::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 0, 0, 0.1);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.6s ease-out;
    z-index: -1;
}

.btn-wishlist.active i:active::after {
    transform: scale(1.5);
    opacity: 0;
}

/* Customize Button Styles */
.btn-customize {
    background-color: #a67f83;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-customize i {
    font-size: 0.9em;
}

.btn-customize:hover {
    background-color: #db9097;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-customize:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) inset;
}

/* Product Actions Container */
.product-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

/* View More Button */
.view-btn {
    background-color: #a67f83;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-block;
    margin-top: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.view-btn:hover {
    background-color: #db9097;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.view-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1) inset;
}

.view-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.view-btn:focus:not(:active)::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}
