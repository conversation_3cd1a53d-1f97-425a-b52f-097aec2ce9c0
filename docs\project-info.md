# 🧲 <PERSON>'s Magnet Website - Project Information

## 📋 Project Overview

This is a custom magnet making website where <PERSON> can promote and sell her handmade magnets online. The website features a complete e-commerce solution with user authentication, product customization, and order management.

## 🎯 Key Features

### 🛍️ E-commerce Functionality
- Product catalog with customizable magnets
- Shopping cart and wishlist
- User accounts and authentication
- Order management system

### 🎨 Product Customization
- Father's Day special magnets
- Wedding collection
- Birthday specials
- Custom text and design options

### 🔐 User Management
- Secure user registration and login
- Database-driven authentication
- Password hashing and JWT tokens
- Admin panel for user management

### 📱 Responsive Design
- Mobile-friendly interface
- Beautiful animations and transitions
- Heart-themed design elements
- Pink/rose color scheme

## 🛠️ Technical Stack

### Frontend
- **HTML5** - Semantic markup
- **CSS3** - Custom styling with animations
- **JavaScript** - Interactive functionality
- **Font Awesome** - Icons and symbols

### Backend
- **Node.js** - Server runtime
- **Express.js** - Web framework
- **SQLite** - Database
- **bcryptjs** - Password hashing
- **JWT** - Authentication tokens

### Security
- Rate limiting
- Input validation
- SQL injection prevention
- CORS protection
- Helmet security headers

## 📁 Project Structure

```
julia-magnet-website/
├── Html pages/          # Website pages
├── Style/              # CSS stylesheets
├── js/                 # JavaScript files
├── images/             # Product images and assets
├── database/           # SQLite database
├── scripts/            # Utility scripts
├── docs/               # Documentation
├── data/               # JSON data files
└── server.js           # Main server file
```

## 🚀 Getting Started

1. **Install Dependencies**: `npm install`
2. **Initialize Database**: `npm run init-db`
3. **Start Server**: `npm start`
4. **Access Website**: `http://localhost:3000`

## 📞 Contact

For questions about this project, please contact Julia Abela:
- Email: <EMAIL>
- Phone: +356 79652171

## 📝 Notes

This website was built with love and attention to detail, just like Julia's handmade magnets. Every feature has been carefully crafted to provide the best user experience for both Julia and her customers.
