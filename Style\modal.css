/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    backdrop-filter: blur(3px);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modal Content */
.modal {
    background: white;
    padding: 25px;
    border-radius: 10px;
    max-width: 90%;
    width: 100%;
    max-width: 400px;
    transform: translateY(20px);
    transition: transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.modal-overlay.active .modal {
    transform: translateY(0);
}

/* Modal Header */
.modal-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    color: #a67f83;
    font-size: 1.5rem;
}

/* Modal Body */
.modal-body {
    margin-bottom: 25px;
    color: #555;
    line-height: 1.6;
}

/* Modal Footer */
.modal-footer {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Modal Buttons */
.modal-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.modal-btn-close {
    background-color: #a67f83;
    color: white;
}

.modal-btn-close:hover {
    background-color: #8c6a6e;
    transform: translateY(-2px);
}

/* Error State */
.modal.error {
    border-left: 5px solid #e74c3c;
}

.modal.error .modal-header h3 {
    color: #e74c3c;
}

.modal.error .modal-body {
    color: #e74c3c;
    font-weight: 500;
}

/* Warning State */
.modal.warning .modal-header h3 {
    color: #f39c12;
}

/* Success State */
.modal.success .modal-header h3 {
    color: #2ecc71;
}

/* Input Error State */
.input-error {
    border-color: #e74c3c !important;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}
