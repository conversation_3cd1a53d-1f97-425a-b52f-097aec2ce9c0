// Search functionality for the website
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const searchInput = document.getElementById('searchInput');
    const searchIcon = document.querySelector('.search-icon');
    const searchContainer = document.getElementById('searchContainer');
    const suggestionsContainer = document.getElementById('searchSuggestions');

    // Keywords for search suggestions
    const searchKeywords = [
        "Father's Day",
        "Father's Day Gifts",
        "Gifts for Dad",
        "Personalized Father's Day",
        "Father's Day Magnets",
        "Best Dad Ever",
        "Father's Day 2024",
        "Handmade Father's Day Gifts",
        "Unique Gifts for Dad",
        "Father's Day Presents"
    ];

    // Initialize search functionality
    function initSearch() {
        // Show/hide search on mobile
        const searchToggle = document.getElementById('searchToggle');
        if (searchToggle) {
            searchToggle.addEventListener('click', toggleSearch);
        }
        
        // Handle search input
        if (searchInput) {
            searchInput.addEventListener('input', handleSearchInput);
            searchInput.addEventListener('focus', showSuggestions);
            
            // Close suggestions when clicking outside
            document.addEventListener('click', (e) => {
                if (!searchContainer.contains(e.target)) {
                    hideSuggestions();
                }
            });
        }
        
        // Handle search icon click
        if (searchIcon) {
            searchIcon.addEventListener('click', () => {
                if (searchInput && searchInput.value.trim()) {
                    performSearch(searchInput.value.trim());
                }
            });
        }
        
        // Handle Enter key press
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && searchInput.value.trim()) {
                    performSearch(searchInput.value.trim());
                }
            });
        }
    }

    function toggleSearch() {
        if (searchContainer) {
            searchContainer.classList.toggle('active');
            if (searchContainer.classList.contains('active') && searchInput) {
                searchInput.focus();
            }
        }
    }

    function handleSearchInput(e) {
        if (!e || !e.target) return;
        
        const query = e.target.value.trim().toLowerCase();
        if (query.length > 0) {
            showSuggestions(query);
        } else {
            hideSuggestions();
        }
    }

    function showSuggestions(query = '') {
        if (!suggestionsContainer) return;
        
        // Clear previous suggestions
        suggestionsContainer.innerHTML = '';
        
        // Get matching products and keywords
        const productMatches = getProductMatches(query);
        const keywordMatches = getKeywordMatches(query);
        
        // Show message if no matches found
        if (productMatches.length === 0 && keywordMatches.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'no-suggestions';
            noResults.textContent = 'No matching products found';
            suggestionsContainer.appendChild(noResults);
        } else {
            // Add keyword suggestions first
            if (keywordMatches.length > 0) {
                addSuggestionHeader('Suggestions');
                keywordMatches.forEach(keyword => {
                    addSuggestionItem(keyword, true);
                });
            }
            
            // Add product matches
            if (productMatches.length > 0) {
                addSuggestionHeader('Products', keywordMatches.length > 0);
                productMatches.slice(0, 5).forEach(product => {
                    addSuggestionItem(product.name, false, product);
                });
            }
        }
        
        // Show suggestions container
        suggestionsContainer.classList.add('visible');
    }

    function addSuggestionHeader(text, addMargin = false) {
        const header = document.createElement('div');
        header.className = 'suggestion-header';
        header.textContent = text;
        if (addMargin) {
            header.style.marginTop = '10px';
        }
        if (suggestionsContainer) {
            suggestionsContainer.appendChild(header);
        }
    }

    function addSuggestionItem(text, isKeyword = false, product = null) {
        if (!suggestionsContainer) return;
        
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        
        const icon = document.createElement('i');
        icon.className = isKeyword ? 'fas fa-search' : 'fas fa-gift';
        item.appendChild(icon);
        
        const textSpan = document.createElement('span');
        textSpan.textContent = text;
        item.appendChild(textSpan);
        
        item.addEventListener('click', () => {
            if (searchInput) {
                searchInput.value = text;
                performSearch(text);
            }
        });
        
        suggestionsContainer.appendChild(item);
    }

    function hideSuggestions() {
        if (suggestionsContainer) {
            suggestionsContainer.classList.remove('visible');
        }
    }

    function getProductMatches(query) {
        if (!query || !window.products) return [];
        return window.products.filter(product => 
            (product.name && product.name.toLowerCase().includes(query)) ||
            (product.category && product.category.toLowerCase().includes(query)) ||
            (product.color && product.color.toLowerCase().includes(query))
        );
    }

    function getKeywordMatches(query) {
        if (!query) return [];
        return searchKeywords.filter(keyword => 
            keyword && keyword.toLowerCase().includes(query)
        ).slice(0, 5);
    }

    function performSearch(query) {
        if (!query) return;
        
        // Simple search redirection - can be enhanced with actual search logic
        window.location.href = `../Html pages/search-results.html?q=${encodeURIComponent(query)}`;
    }

    // Initialize the search functionality
    initSearch();
});
