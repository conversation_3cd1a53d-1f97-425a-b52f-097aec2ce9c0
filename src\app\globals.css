@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-magnet-red via-purple-600 to-magnet-blue min-h-screen;
  }
}

@layer components {
  .magnet-card {
    @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20;
  }
  
  .magnet-button {
    @apply bg-magnet-gradient text-white font-semibold py-3 px-6 rounded-lg 
           hover:shadow-lg transform hover:scale-105 transition-all duration-200
           focus:outline-none focus:ring-4 focus:ring-purple-300;
  }
  
  .magnet-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 
           focus:ring-magnet-blue focus:border-transparent outline-none
           transition-all duration-200;
  }
}
