# 👥 User Management Guide

This guide shows you all the ways to view and manage users in your database.

## 🌐 Method 1: Web-Based Admin Panel (Easiest)

### Access the Admin Panel
1. Make sure your server is running (`npm start`)
2. Open your browser and go to: **`http://localhost:3000/admin/users`**

### What You'll See:
- 📊 **Dashboard Statistics**: Total users, active users, verified emails, active sessions
- 📋 **User Table**: Complete list of all registered users with:
  - User ID
  - Full name
  - Email address
  - Registration date
  - Last login date
  - Account status (Active/Inactive)
  - Email verification status

### Features:
- ✅ Real-time data (click refresh to update)
- ✅ Clean, easy-to-read interface
- ✅ Responsive design works on mobile
- ✅ No technical knowledge required

---

## 💻 Method 2: Command Line Tools

### View All Users
```bash
npm run view-users
```

**Output Example:**
```
👥 Found 3 user(s):

═══════════════════════════════════════════════════════════════════════════════════════════════════
👤 User #1
   ID: 3
   Name: Julia Abela
   Email: <EMAIL>
   Registered: 12/15/2024, 2:30:45 PM (2 hours ago)
   Last Login: 12/15/2024, 4:15:22 PM (15 minutes ago)
   Status: ✅ Active
   Email Verified: ✅ Yes
──────────────────────────────────────────────────────────────────────────────
👤 User #2
   ID: 2
   Name: Test User
   Email: <EMAIL>
   Registered: 12/15/2024, 10:00:00 AM (6 hours ago)
   Last Login: Never
   Status: ✅ Active
   Email Verified: ❌ No
──────────────────────────────────────────────────────────────────────────────

🔐 Session Statistics:
   Total Sessions: 5
   Active Sessions: 2
```

### View Detailed Statistics
```bash
npm run user-stats
```

**Output Example:**
```
📈 Generating user statistics...

═══════════════════════════════════════════════════════════════
📊 USER STATISTICS DASHBOARD
═══════════════════════════════════════════════════════════════

👥 USER OVERVIEW:
   Total Users: 15
   Active Users: 14
   Verified Emails: 8
   Users Who Logged In: 12

📈 PERCENTAGES:
   Active Rate: 93.3%
   Verification Rate: 53.3%
   Login Rate: 80.0%

🔐 SESSION STATISTICS:
   Total Sessions: 28
   Active Sessions: 5
   Sessions Today: 8

📅 RECENT REGISTRATIONS (Last 30 days):
   2024-12-15: 3 users
   2024-12-14: 2 users
   2024-12-13: 1 user

🔑 RECENT LOGIN ACTIVITY (Last 30 days):
   2024-12-15: 5 logins
   2024-12-14: 3 logins
   2024-12-13: 2 logins

🆕 NEWEST USERS:
   1. Julia Abela (<EMAIL>) - 12/15/2024
   2. Sarah Johnson (<EMAIL>) - 12/14/2024
   3. Mike Wilson (<EMAIL>) - 12/14/2024
```

---

## 🗄️ Method 3: Direct Database Access (Advanced)

### Using SQLite Command Line
```bash
# Open the database
sqlite3 database/users.db

# View all users
SELECT * FROM users;

# View users with formatted output
.mode column
.headers on
SELECT id, name, email, registration_date, last_login FROM users;

# Count users
SELECT COUNT(*) as total_users FROM users;

# Find users by email
SELECT * FROM users WHERE email LIKE '%gmail.com';

# Exit SQLite
.quit
```

### Using a SQLite Browser (Recommended for Advanced Users)
1. Download **DB Browser for SQLite** (free): https://sqlitebrowser.org/
2. Open the file: `database/users.db`
3. Browse tables, run queries, export data

---

## 📊 Understanding the Data

### Users Table Fields:
- **`id`**: Unique user identifier (auto-incrementing)
- **`name`**: User's full name
- **`email`**: User's email address (unique)
- **`password`**: Encrypted password (bcrypt hash)
- **`registration_date`**: When the user signed up
- **`last_login`**: When the user last logged in
- **`is_active`**: Whether the account is active (1 = active, 0 = inactive)
- **`email_verified`**: Whether the email is verified (1 = verified, 0 = not verified)

### Sessions Table Fields:
- **`id`**: Unique session identifier
- **`user_id`**: Links to the user
- **`token`**: JWT authentication token
- **`expires_at`**: When the session expires
- **`created_at`**: When the session was created

---

## 🔍 Common Queries

### Find a Specific User
```bash
# Using command line
npm run view-users | grep "<EMAIL>"

# Using SQLite
sqlite3 database/users.db "SELECT * FROM users WHERE email = '<EMAIL>';"
```

### Check Recent Registrations
```bash
# Using SQLite
sqlite3 database/users.db "SELECT name, email, registration_date FROM users WHERE registration_date >= datetime('now', '-7 days') ORDER BY registration_date DESC;"
```

### Find Active Sessions
```bash
# Using SQLite
sqlite3 database/users.db "SELECT u.name, u.email, s.created_at FROM users u JOIN user_sessions s ON u.id = s.user_id WHERE s.expires_at > datetime('now');"
```

---

## 🛠️ Quick Actions

### Refresh User Data
- **Web Panel**: Click the "Refresh" button
- **Command Line**: Re-run `npm run view-users`

### Export User List
```bash
# Export to CSV
sqlite3 -header -csv database/users.db "SELECT id, name, email, registration_date FROM users;" > users.csv
```

### Backup Database
```bash
# Create backup
cp database/users.db database/users_backup_$(date +%Y%m%d).db
```

---

## 🚨 Troubleshooting

### "Database not found" Error
```bash
# Initialize the database first
npm run init-db
```

### "Permission denied" Error
```bash
# Check file permissions (Mac/Linux)
ls -la database/
chmod 644 database/users.db
```

### Empty User List
- Users only appear after they register on your website
- Check that your server is running and accessible
- Try creating a test account through the login page

---

## 💡 Pro Tips

1. **Bookmark the admin panel**: `http://localhost:3000/admin/users`
2. **Use command line for quick checks**: `npm run view-users`
3. **Monitor with stats**: `npm run user-stats` for insights
4. **Regular backups**: Copy the `database/users.db` file
5. **Security**: Never share the database file or admin panel publicly

The admin panel is the easiest way to view users, while command line tools give you more detailed information!
