document.addEventListener('DOMContentLoaded', function() {
    // Create modal HTML for subscription
    const modalHTML = `
        <div class="modal-overlay" id="subscriptionModal">
            <div class="modal">
                <div class="modal-header">
                    <h3>Subscription Status</h3>
                </div>
                <div class="modal-body" id="subscriptionMessage">
                    <!-- Message will be inserted here -->
                </div>
                <div class="modal-footer">
                    <button class="modal-btn modal-btn-close">Close</button>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to the body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Get modal elements
    const modal = document.getElementById('subscriptionModal');
    const modalMessage = document.getElementById('subscriptionMessage');
    const modalCloseBtn = modal.querySelector('.modal-btn-close');
    
    // Close modal function
    function closeModal() {
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
    }
    
    // Show modal function
    function showModal(message, type = 'success') {
        modalMessage.textContent = message;
        const modalElement = modal.querySelector('.modal');
        modalElement.className = 'modal';
        
        // Add the appropriate class based on type
        if (type === 'error' || type === 'warning' || type === 'success') {
            modalElement.classList.add(type);
        } else {
            modalElement.classList.add('success');
        }
        
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Focus the close button for better accessibility
        setTimeout(() => {
            modalCloseBtn.focus();
        }, 100);
    }
    
    // Close modal when clicking the close button
    modalCloseBtn.addEventListener('click', closeModal);
    
    // Close modal when clicking outside the modal content
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('active')) {
            closeModal();
        }
    });
    
    /**
     * Validates the format of an email address.
     * @param {string} emailValue The email string to validate.
     * @param {HTMLInputElement} emailInput The input element for focusing and styling on error.
     * @returns {boolean} True if the email format is valid, false otherwise.
     */
    function isEmailFormatValid(emailValue, emailInput) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailValue)) {
            showModal('Please enter a valid email address (e.g., <EMAIL>).', 'error');
            emailInput.classList.add('input-error');
            emailInput.focus();
            return false;
        }
        return true;
    }

    /**
     * Validates the domain of an email address against an allowed list.
     * @param {string} emailValue The email string to validate.
     * @param {HTMLInputElement} emailInput The input element for focusing and styling on error.
     * @returns {boolean} True if the email domain is allowed, false otherwise.
     */
    function isEmailDomainValid(emailValue, emailInput) {
        const allowedDomains = ['gmail.com', 'icloud.com'];
        const domain = emailValue.split('@')[1]?.toLowerCase();
        if (!domain || !allowedDomains.includes(domain)) {
            showModal('Please use a @gmail.com or @icloud.com email address.', 'warning');
            emailInput.classList.add('input-error');
            emailInput.focus();
            return false;
        }
        return true;
    }

    // Handle newsletter form submissions
    document.addEventListener('submit', function(e) {
        const form = e.target.closest('form');
        if (!form || !form.closest('.newsletter')) {
            return;
        }
        
        e.preventDefault();
        
        const emailInput = form.querySelector('input[type="email"]');
        if (!emailInput) return;
        
        emailInput.classList.remove('input-error');
        const email = emailInput.value.trim();
        
        if (email === '') {
            showModal('Please enter your email address.', 'error');
            emailInput.classList.add('input-error');
            emailInput.focus();
            return;
        }
        
        // Validate email format and domain
        if (!isEmailFormatValid(email, emailInput) || !isEmailDomainValid(email, emailInput)) {
            return;
        }
        
        // Show success message
        showModal('Thank you for subscribing to our newsletter! You\'ll receive updates soon.');
        
        // Prepare and send form data
        const formData = new FormData(form);
        
        fetch('https://formsubmit.co/ajax/<EMAIL>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Subscription form submitted successfully:', data);
            form.reset();
        })
        .catch(error => {
            console.error('Error submitting subscription form:', error);
            showModal('There was an error submitting the form. Please try again later.', 'error');
        });
    });
    
    // Remove error state when typing in newsletter email input
    document.addEventListener('input', function(e) {
        if (e.target.matches('.newsletter input[type="email"]')) {
            e.target.classList.remove('input-error');
        }
    });
});
