/* Contact Page Styles */
.contact-hero {
    background: white;
    padding: 80px 20px;
    text-align: center;
    margin-bottom: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.contact-hero h1 {
    color: #a67f83;
    font-size: 2.5rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.contact-hero p {
    color: #6d4c4f;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto 60px;
    padding: 0 20px;
}

.contact-info {
    flex: 1;
    min-width: 300px;
    background: #fff9fa;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(166, 127, 131, 0.1);
}

.info-box {
    margin-bottom: 30px;
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.info-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(166, 127, 131, 0.15);
}

.info-box i {
    font-size: 2rem;
    color: #ff6b8b;
    margin-bottom: 15px;
    display: block;
}

.info-box h3 {
    color: #a67f83;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.info-box p {
    color: #6d4c4f;
    line-height: 1.6;
    margin: 0;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f0e6e7;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ffebee;
    color: #a67f83;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-icon:hover {
    background: #ff6b8b;
    color: white;
    transform: translateY(-3px);
}

.contact-form {
    flex: 2;
    min-width: 300px;
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(166, 127, 131, 0.1);
}

.contact-form h2 {
    color: #a67f83;
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
    position: relative;
    padding-bottom: 15px;
}

.contact-form h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #ff6b8b;
    border-radius: 3px;
}

.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0c8cb;
    border-radius: 6px;
    font-size: 1rem;
    color: #6d4c4f;
    background: #fff9fa;
    transition: all 0.3s ease;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.form-group label {
    position: absolute;
    left: 15px;
    top: 12px;
    color: #a67f83;
    transition: all 0.3s ease;
    pointer-events: none;
    background: #fff9fa;
    padding: 0 5px;
    border-radius: 4px;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #ff6b8b;
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 107, 139, 0.2);
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -10px;
    font-size: 0.8rem;
    color: #ff6b8b;
    background: white;
}

.submit-btn {
    background: #ff6b8b;
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1rem;
    border-radius: 30px;
    cursor: pointer;
    display: block;
    margin: 0 auto;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(255, 107, 139, 0.3);
}

.submit-btn:hover {
    background: #ff4d73;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 139, 0.4);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .contact-container {
        flex-direction: column;
    }
    
    .contact-info,
    .contact-form {
        width: 100%;
        margin-bottom: 30px;
    }
    
    .contact-hero h1 {
        font-size: 2rem;
    }
    
    .contact-hero p {
        font-size: 1rem;
    }
}

/* Form validation styles */
.form-group input:invalid:not(:focus):not(:placeholder-shown),
.form-group textarea:invalid:not(:focus):not(:placeholder-shown) {
    border-color: #ff6b8b;
}

.form-group input:valid:not(:focus):not(:placeholder-shown),
.form-group textarea:valid:not(:focus):not(:placeholder-shown) {
    border-color: #a0d8b3;
}
