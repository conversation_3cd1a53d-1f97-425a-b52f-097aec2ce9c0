document.addEventListener('DOMContentLoaded', function() {
    const cartItemsList = document.getElementById('cartItemsList');
    const emptyCartMessage = document.getElementById('emptyCartMessage');
    const cartSummary = document.getElementById('cartSummary');
    const cartSubtotal = document.getElementById('cartSubtotal');
    const cartTotal = document.getElementById('cartTotal');
    const checkoutBtn = document.getElementById('checkoutBtn');
    
    // Initialize cart count on page load
    updateCartCount();

    // Display cart items
    function displayCartItems() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        
        if (cart.length === 0) {
            emptyCartMessage.style.display = 'block';
            cartItemsList.style.display = 'none';
            cartSummary.style.display = 'none';
            return;
        }

        emptyCartMessage.style.display = 'none';
        cartItemsList.style.display = 'block';
        cartSummary.style.display = 'block';
        
        // Clear existing items
        cartItemsList.innerHTML = '';
        
        // Calculate subtotal and display items
        let subtotal = 0;
        
        cart.forEach((item, index) => {
            subtotal += parseFloat(item.total);
            
            const cartItem = document.createElement('div');
            cartItem.className = 'cart-item';
            cartItem.dataset.id = item.id;
            
            cartItem.innerHTML = `
                <div class="cart-item-image">
                    ${item.image ? `<img src="${item.image}" alt="Custom Magnet">` : 
                      `<div class="no-image" style="background-color: ${item.magnetColor}; width: 100px; height: 100px; display: flex; align-items: center; justify-content: center; color: white;">
                          <i class="fas fa-image"></i>
                       </div>`}
                </div>
                <div class="cart-item-details">
                    <h4>Custom Magnet</h4>
                    <div class="cart-item-options">
                        <p><strong>Color:</strong> <span style="display: inline-block; width: 15px; height: 15px; background-color: ${item.magnetColor}; border: 1px solid #ddd;"></span></p>
                        <p><strong>Text Color:</strong> <span style="display: inline-block; width: 15px; height: 15px; background-color: ${item.textColor}; border: 1px solid #ddd;"></span></p>
                        <p><strong>Language:</strong> ${item.language === 'en' ? 'English' : 'Maltese'}</p>
                    </div>
                </div>
                <div class="cart-item-quantity">
                    <button class="quantity-btn decrease" data-id="${item.id}">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn increase" data-id="${item.id}">+</button>
                </div>
                <div class="cart-item-price">
                    $${item.total}
                </div>
                <button class="remove-item" data-id="${item.id}" aria-label="Remove item">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            cartItemsList.appendChild(cartItem);
        });
        
        // Update subtotal and total
        cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
        cartTotal.textContent = `$${subtotal.toFixed(2)}`; // Add shipping if needed
        
        // Enable/disable checkout button
        checkoutBtn.disabled = cart.length === 0;
        
        // Add event listeners for quantity and remove buttons
        addCartEventListeners();
    }
    
    // Add event listeners for cart actions
    function addCartEventListeners() {
        // Remove item - using event delegation for dynamically added elements
        document.addEventListener('click', function(e) {
            if (e.target.closest('.remove-item')) {
                const itemId = e.target.closest('.remove-item').getAttribute('data-id');
                removeFromCart(itemId);
            }
        });
        
        // Decrease quantity
        document.querySelectorAll('.decrease').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                updateQuantity(itemId, -1);
            });
        });
        
        // Increase quantity
        document.querySelectorAll('.increase').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                updateQuantity(itemId, 1);
            });
        });
    }
    
    // Remove item from cart
    function removeFromCart(itemId) {
        if (!confirm('Are you sure you want to remove this item from your cart?')) {
            return;
        }
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        cart = cart.filter(item => item.id !== itemId);
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartCount();
        displayCartItems();
        
        // Show a message when cart is empty
        if (cart.length === 0) {
            alert('Item removed. Your cart is now empty.');
        }
    }
    
    // Update item quantity
    function updateQuantity(itemId, change) {
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        const itemIndex = cart.findIndex(item => item.id === itemId);
        
        if (itemIndex > -1) {
            const newQuantity = cart[itemIndex].quantity + change;
            
            if (newQuantity < 1) {
                // If quantity would be less than 1, remove the item
                cart.splice(itemIndex, 1);
            } else {
                // Update quantity and total
                cart[itemIndex].quantity = newQuantity;
                cart[itemIndex].total = (cart[itemIndex].price * newQuantity).toFixed(2);
            }
            
            const cartItem = {
                id: cart[itemIndex].id,
                type: cart[itemIndex].type,
                image: cart[itemIndex].image,
                magnetColor: cart[itemIndex].magnetColor,
                textColor: cart[itemIndex].textColor,
                language: cart[itemIndex].language,
                quantity: cart[itemIndex].quantity,
                price: cart[itemIndex].price,
                total: cart[itemIndex].total,
                addedAt: cart[itemIndex].addedAt,
                name: cart[itemIndex].name
            };
            
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            displayCartItems();
        }
    }
    
    // Update cart count in header
    function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
        document.querySelectorAll('.cart-count').forEach(el => {
            const currentCount = parseInt(el.textContent) || 0;
            el.textContent = cartCount;
            
            // Only animate if count has changed
            if (currentCount !== cartCount) {
                // Add animation class
                el.classList.add('updated');
                
                // Remove animation class after it completes
                setTimeout(() => {
                    el.classList.remove('updated');
                }, 300);
            }
        });
    }
    
    // Initialize cart
    displayCartItems();
    
    // Handle checkout button
    checkoutBtn.addEventListener('click', function() {
        // Add checkout logic here
        alert('Proceeding to checkout...');
    });
});
