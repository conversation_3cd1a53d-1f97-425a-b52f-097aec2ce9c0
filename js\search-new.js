document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const searchInput = document.getElementById('searchInput');
    const searchIcon = document.querySelector('.search-icon');
    const searchContainer = document.querySelector('.search-container');
    const searchToggle = document.getElementById('searchToggle');
    const suggestionsContainer = document.getElementById('searchSuggestions');

    // Keywords for search suggestions
    const searchKeywords = [
        "Father's Day",
        "Father's Day Gifts",
        "Gifts for Dad",
        "Personalized Father's Day",
        "Father's Day Magnets",
        "Best Dad Ever",
        "Father's Day 2024",
        "Handmade Father's Day Gifts",
        "Unique Gifts for Dad",
        "Father's Day Presents"
    ];

    // Toggle search on mobile
    if (searchToggle) {
        searchToggle.addEventListener('click', function(e) {
            e.preventDefault();
            searchContainer.classList.toggle('visible');
            if (searchContainer.classList.contains('visible')) {
                searchInput.focus();
            }
        });
    }

    // Handle search input
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim().toLowerCase();
            if (query.length > 0) {
                showSuggestions(query);
            } else {
                hideSuggestions();
            }
        });

        // Handle Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && searchInput.value.trim()) {
                performSearch(searchInput.value.trim());
            }
        });
    }

    // Handle search icon click
    if (searchIcon) {
        searchIcon.addEventListener('click', function() {
            if (searchInput && searchInput.value.trim()) {
                performSearch(searchInput.value.trim());
            }
        });
    }

    // Close suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchContainer.contains(e.target) && !e.target.closest('.search-suggestions')) {
            hideSuggestion();
        }
    });

    function showSuggestions(query) {
        if (!suggestionsContainer) return;
        
        // Clear previous suggestions
        suggestionsContainer.innerHTML = '';
        
        // Get matching products and keywords
        const productMatches = getProductMatches(query);
        const keywordMatches = getKeywordMatches(query);
        
        // Show message if no matches found
        if (productMatches.length === 0 && keywordMatches.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'no-suggestions';
            noResults.textContent = 'No matching products found';
            suggestionsContainer.appendChild(noResults);
        } else {
            // Add keyword suggestions first
            if (keywordMatches.length > 0) {
                const keywordHeader = document.createElement('div');
                keywordHeader.className = 'suggestion-header';
                keywordHeader.textContent = 'Suggestions';
                suggestionsContainer.appendChild(keywordHeader);
                
                keywordMatches.forEach(keyword => {
                    const suggestion = document.createElement('div');
                    suggestion.className = 'suggestion-item';
                    suggestion.innerHTML = `<i class="fas fa-search"></i> ${keyword}`;
                    suggestion.addEventListener('click', () => {
                        searchInput.value = keyword;
                        performSearch(keyword);
                    });
                    suggestionsContainer.appendChild(suggestion);
                });
            }
            
            // Add product matches
            if (productMatches.length > 0) {
                const productHeader = document.createElement('div');
                productHeader.className = 'suggestion-header';
                productHeader.textContent = 'Products';
                if (keywordMatches.length > 0) {
                    productHeader.style.marginTop = '10px';
                }
                suggestionsContainer.appendChild(productHeader);
                
                productMatches.slice(0, 5).forEach(product => {
                    const suggestion = document.createElement('div');
                    suggestion.className = 'suggestion-item';
                    suggestion.innerHTML = `<i class="fas fa-gift"></i> ${product.name}`;
                    suggestion.addEventListener('click', () => {
                        window.location.href = `product.html?id=${product.id}`;
                    });
                    suggestionsContainer.appendChild(suggestion);
                });
            }
        }
        
        // Show suggestions container
        suggestionsContainer.classList.add('visible');
    }

    function hideSuggestion() {
        if (suggestionsContainer) {
            suggestionsContainer.classList.remove('visible');
        }
    }

    function getProductMatches(query) {
        if (!query || !window.products) return [];
        return window.products.filter(product => 
            (product.name && product.name.toLowerCase().includes(query)) ||
            (product.category && product.category.toLowerCase().includes(query)) ||
            (product.color && product.color.toLowerCase().includes(query))
        );
    }

    function getKeywordMatches(query) {
        if (!query) return [];
        return searchKeywords.filter(keyword => 
            keyword && keyword.toLowerCase().includes(query)
        ).slice(0, 5);
    }

    function performSearch(query) {
        if (!query) return;
        window.location.href = `search-results.html?q=${encodeURIComponent(query)}`;
    }
});
