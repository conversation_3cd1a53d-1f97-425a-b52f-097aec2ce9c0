:root {
    --primary-color: #ff6b6b;
    --primary-hover: #ff5252;
    --secondary-color: #4ecdc4;
    --text-color: #2d3436;
    --text-light: #636e72;
    --background: #f9f9f9;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --success-color: #00b894;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
}

.cart-page {
    padding: 2rem 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 60vh;
}

.cart-page h1 {
    text-align: center;
    margin-bottom: 2.5rem;
    color: var(--text-color);
    font-size: 2.5rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

.cart-page h1:after {
    content: '';
    position: absolute;
    width: 60%;
    height: 4px;
    background: var(--primary-color);
    bottom: -10px;
    left: 20%;
    border-radius: 2px;
}

.cart-container {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 2.5rem;
    align-items: flex-start;
}

/* Cart Items */
.cart-items {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.cart-empty {
    text-align: center;
    padding: 4rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
}

.cart-empty i {
    font-size: 4.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    opacity: 0.7;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
    40% {transform: translateY(-15px);}
    60% {transform: translateY(-7px);}
}

.cart-empty h2 {
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
    font-weight: 700;
}

.cart-empty p {
    color: var(--text-light);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    max-width: 400px;
    line-height: 1.6;
}

.cart-items-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cart-item {
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    display: grid;
    grid-template-columns: 120px 1fr auto auto;
    gap: 1.5rem;
    align-items: center;
    background: var(--card-bg);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.cart-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.cart-item-image {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f8f8;
    border: 1px solid var(--border-color);
    padding: 0.5rem;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    transition: var(--transition);
}

.cart-item:hover .cart-item-image img {
    transform: scale(1.05);
}

.cart-item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding-right: 1rem;
}

.cart-item-options {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    margin-top: 0.5rem;
}

.cart-item-options p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 50px;
    border: 1px solid var(--border-color);
}

.cart-item-quantity .quantity {
    min-width: 25px;
    text-align: center;
}

.quantity-btn {
    background: transparent;
    border: none;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1rem;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: #e9ecef;
}

.remove-item {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 1.1rem;
    padding: 0.5rem;
    transition: all 0.2s ease;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-item:hover {
    background: #f8d7da;
    color: #b02a37;
}

.cart-item-price {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.25rem;
    min-width: 100px;
    text-align: right;
    padding-right: 1rem;
}

.cart-item-title {
    font-size: 1.2rem;
    color: var(--text-color);
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    transition: var(--transition);
}

.cart-item-price {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.25rem;
    margin: 0.5rem 0;
}

.cart-item-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 50px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.quantity-btn {
    background: transparent;
    border: none;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.1rem;
    color: var(--text-color);
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--background);
    color: var(--primary-color);
}

.quantity-input {
    width: 40px;
    height: 36px;
    text-align: center;
    border: none;
    background: transparent;
    font-size: 1rem;
    color: var(--text-color);
    -moz-appearance: textfield;
    -webkit-appearance: none;
    appearance: none;
    padding: 0;
}

.quantity-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-color);
    border-radius: 4px;
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.remove-item {
    background: rgba(255, 107, 107, 0.1);
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    transition: var(--transition);
    font-weight: 500;
}

.remove-item i {
    margin-right: 6px;
}

.remove-item:hover {
    background: rgba(255, 82, 82, 0.15);
    transform: translateY(-1px);
}

/* Cart Summary */
.cart-summary {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    position: sticky;
    top: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.cart-summary h3 {
    margin: 0 0 1.5rem 0;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 1.4rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-summary h3 i {
    color: var(--primary-color);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    color: var(--text-light);
    font-size: 1rem;
}

.summary-total {
    font-weight: 800;
    font-size: 1.4rem;
    color: var(--text-color);
    margin: 1.5rem 0;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkout-btn {
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.checkout-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.checkout-btn:active {
    transform: translateY(0);
}

.checkout-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.7;
}

.continue-shopping {
    text-align: center;
    margin: 0;
}

.continue-shopping a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.continue-shopping a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Success Message */
.success-message {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--success-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1000;
    animation: slideIn 0.3s ease-out forwards;
    max-width: 350px;
}

.success-message i {
    font-size: 1.5rem;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .cart-container {
        grid-template-columns: 1fr;
    }
    
    .cart-summary {
        position: static;
    }
}

@media (max-width: 768px) {
    .cart-page {
        padding: 1.5rem 1rem;
    }
    
    .cart-page h1 {
        font-size: 2rem;
    }
    
    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 1.5rem;
    }
    
    .cart-item-image {
        width: 100%;
        height: 200px;
        margin-bottom: 1rem;
    }
    
    .cart-item-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .cart-summary {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .cart-item {
        padding: 1rem;
    }
    
    .cart-item-image {
        height: 160px;
    }
    
    .cart-item-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .remove-item {
        padding: 0.4rem 0.8rem;
    }
    
    .success-message {
        left: 1rem;
        right: 1rem;
        bottom: 1rem;
        max-width: none;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
    transition: opacity 0.3s ease;
}

.loading-spinner {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.3s ease-out;
}

.spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 1rem;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Cart Item Animations */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.cart-item {
    animation: fadeIn 0.3s ease-out forwards;
}

.checkout-btn:not(:disabled):hover {
    background: #c0392b;
}

.continue-shopping {
    display: block;
    text-align: center;
    margin-top: 1rem;
    color: #3498db;
    text-decoration: none;
    font-size: 0.95rem;
}

.continue-shopping:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (min-width: 768px) {
    .cart-container {
        grid-template-columns: 2fr 1fr;
    }
    
    .cart-page {
        padding: 3rem 2rem;
    }
}

/* Success message */
.success-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #4CAF50;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -20px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

/* Cart item hover effect */
.cart-item {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.cart-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Remove button hover effect */
.remove-item {
    transition: color 0.2s ease, transform 0.2s ease;
}

.remove-item:hover {
    color: #c0392b !important;
    transform: scale(1.05);
}

/* Quantity controls */
.quantity-controls {
    display: flex;
    align-items: center;
}

.quantity-btn {
    background: #f5f5f5;
    border: 1px solid #ddd;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: #e0e0e0;
}

.quantity-input {
    width: 40px;
    height: 30px;
    text-align: center;
    border: 1px solid #ddd;
    margin: 0 5px;
    -webkit-appearance: none;
    -moz-appearance: textfield;
    appearance: textfield;
}

.quantity-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Responsive styles */
@media (max-width: 767px) {
    .cart-item {
        padding: 1rem 0;
    }
    
    .cart-item-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
    }
    
    .quantity-controls {
        margin: 0;
    }
    
    .remove-item {
        margin: 0;
    }
}
