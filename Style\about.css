/* About Page Specific Styles */
.about-section {
    padding: 50px 20px;
    max-width: 1000px;
    margin: 0 auto;
}

.about-content {
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 40px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.about-content h1 {
    color: #d48a8a;
    margin-bottom: 20px;
    text-align: center;
    font-size: 2.5em;
}

.about-content p {
    line-height: 1.8;
    margin-bottom: 20px;
    font-size: 1.1em;
    color: #555;
}

.why-choose-us {
    margin-top: 50px;
}

.why-choose-us h2 {
    text-align: center;
    color: #f7e7ce;
    margin-bottom: 30px;
    font-size: 2em;
}

.features-grid {
    display: flex;
    gap: 25px;
    margin-top: 30px;
    overflow-x: auto;
    padding-bottom: 20px;
    scrollbar-width: thin;
    scrollbar-color: #d48a8a #f9e8e8;
}

.features-grid::-webkit-scrollbar {
    height: 8px;
}

.features-grid::-webkit-scrollbar-track {
    background: #f9e8e8;
    border-radius: 10px;
}

.features-grid::-webkit-scrollbar-thumb {
    background-color: #d48a8a;
    border-radius: 10px;
}

.feature-card {
    flex: 0 0 calc(33.333% - 17px);
    min-width: 250px;
    box-sizing: border-box;
}

.feature-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-right: 25px;
}

/* Hover effect removed as per request */

.feature-icon {
    width: 80px;
    height: 80px;
    background: #f9e8e8;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 30px;
    color: #d48a8a;
}

.feature-card h3 {
    color: #d48a8a;
    margin-bottom: 15px;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Two Column Section */
.two-column-section {
    display: flex;
    justify-content: space-between;
    margin: 60px 0;
    gap: 40px;
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.column {
    flex: 1;
}

.column h3 {
    color: #d48a8a;
    margin-bottom: 20px;
    font-size: 1.8em;
}

.column p {
    margin-bottom: 15px;
    line-height: 1.7;
    color: #555;
}

.divider {
    width: 2px;
    background: linear-gradient(to bottom, transparent, #e0c9c9, transparent);
    margin: 0 20px;
}

.right-column h3::after {
    right: 0;
    left: auto;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .features-grid {
        flex-wrap: nowrap;
        padding-bottom: 25px;
    }
    
    .feature-card {
        flex: 0 0 80%;
        margin-right: 15px;
    }
    
    .two-column-section {
        flex-direction: column;
        padding: 25px 20px;
        gap: 30px;
    }
    
    .divider {
        width: 100%;
        height: 2px;
        margin: 10px 0;
        background: linear-gradient(to right, transparent, #e0c9c9, transparent);
    }
    
    .column h3 {
        font-size: 1.5em;
    }
}
