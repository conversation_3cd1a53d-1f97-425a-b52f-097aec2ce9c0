// Login and Registration Functionality with Local Storage

class AuthManager {
    constructor() {
        this.users = this.loadUsers();
        this.currentUser = this.loadCurrentUser();
        this.initializeEventListeners();
        this.checkAuthStatus();
    }

    // Load users from localStorage
    loadUsers() {
        const users = localStorage.getItem('withLoveUsers');
        return users ? JSON.parse(users) : [];
    }

    // Save users to localStorage
    saveUsers() {
        localStorage.setItem('withLoveUsers', JSON.stringify(this.users));
    }

    // Load current user from localStorage
    loadCurrentUser() {
        const user = localStorage.getItem('withLoveCurrentUser');
        return user ? JSON.parse(user) : null;
    }

    // Save current user to localStorage
    saveCurrentUser(user) {
        localStorage.setItem('withLoveCurrentUser', JSON.stringify(user));
        this.currentUser = user;
    }

    // Remove current user from localStorage
    removeCurrentUser() {
        localStorage.removeItem('withLoveCurrentUser');
        this.currentUser = null;
    }

    // Initialize event listeners
    initializeEventListeners() {
        // Form elements
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const showRegisterLink = document.getElementById('showRegister');
        const showLoginLink = document.getElementById('showLogin');
        const passwordToggles = document.querySelectorAll('.password-toggle');

        // Form submissions
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }
        
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // Toggle between login and register forms
        if (showRegisterLink) {
            showRegisterLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showRegisterForm();
            });
        }

        if (showLoginLink) {
            showLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLoginForm();
            });
        }

        // Password visibility toggles
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => this.togglePasswordVisibility(e));
        });

        // Real-time password confirmation validation
        const confirmPassword = document.getElementById('confirmPassword');
        const regPassword = document.getElementById('regPassword');
        
        if (confirmPassword && regPassword) {
            confirmPassword.addEventListener('input', () => {
                this.validatePasswordMatch();
            });
            regPassword.addEventListener('input', () => {
                this.validatePasswordMatch();
            });
        }
    }

    // Handle login form submission
    handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        // Validate inputs
        if (!email || !password) {
            this.showMessage('Please fill in all fields', 'error');
            return;
        }

        // Find user
        const user = this.users.find(u => u.email.toLowerCase() === email.toLowerCase());
        
        if (!user) {
            this.showMessage('No account found with this email', 'error');
            return;
        }

        if (user.password !== password) {
            this.showMessage('Incorrect password', 'error');
            return;
        }

        // Successful login
        const loginData = {
            id: user.id,
            name: user.name,
            email: user.email,
            loginTime: new Date().toISOString(),
            rememberMe: rememberMe
        };

        this.saveCurrentUser(loginData);
        this.showMessage(`Welcome back, ${user.name}!`, 'success');
        
        // Redirect after short delay
        setTimeout(() => {
            window.location.href = '/Html pages/index.html';
        }, 1500);
    }

    // Handle registration form submission
    handleRegister(e) {
        e.preventDefault();
        
        const name = document.getElementById('regName').value.trim();
        const email = document.getElementById('regEmail').value.trim();
        const password = document.getElementById('regPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // Validate inputs
        if (!name || !email || !password || !confirmPassword) {
            this.showMessage('Please fill in all fields', 'error');
            return;
        }

        if (password !== confirmPassword) {
            this.showMessage('Passwords do not match', 'error');
            return;
        }

        if (password.length < 6) {
            this.showMessage('Password must be at least 6 characters long', 'error');
            return;
        }

        // Check if email already exists
        if (this.users.find(u => u.email.toLowerCase() === email.toLowerCase())) {
            this.showMessage('An account with this email already exists', 'error');
            return;
        }

        // Create new user
        const newUser = {
            id: Date.now().toString(),
            name: name,
            email: email,
            password: password,
            registrationDate: new Date().toISOString()
        };

        this.users.push(newUser);
        this.saveUsers();

        this.showMessage('Account created successfully! Please sign in.', 'success');
        
        // Switch to login form after short delay
        setTimeout(() => {
            this.showLoginForm();
            // Pre-fill email
            document.getElementById('email').value = email;
        }, 1500);
    }

    // Show register form
    showRegisterForm() {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const registerLink = document.querySelector('.register-link');
        const loginLink = document.getElementById('loginLink');
        const loginHeader = document.querySelector('.login-header');

        loginForm.classList.add('hidden');
        registerLink.classList.add('hidden');
        registerForm.classList.remove('hidden');
        loginLink.classList.remove('hidden');

        // Update header
        loginHeader.querySelector('h2').textContent = 'Create Account';
        loginHeader.querySelector('p').textContent = 'Join our community';
    }

    // Show login form
    showLoginForm() {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const registerLink = document.querySelector('.register-link');
        const loginLink = document.getElementById('loginLink');
        const loginHeader = document.querySelector('.login-header');

        registerForm.classList.add('hidden');
        loginLink.classList.add('hidden');
        loginForm.classList.remove('hidden');
        registerLink.classList.remove('hidden');

        // Update header
        loginHeader.querySelector('h2').textContent = 'Welcome Back';
        loginHeader.querySelector('p').textContent = 'Sign in to your account';

        // Clear form
        registerForm.reset();
    }

    // Toggle password visibility
    togglePasswordVisibility(e) {
        const button = e.currentTarget;
        const input = button.parentElement.querySelector('input');
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Validate password match in real-time
    validatePasswordMatch() {
        const password = document.getElementById('regPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const confirmInput = document.getElementById('confirmPassword');

        if (confirmPassword && password !== confirmPassword) {
            confirmInput.style.borderColor = '#ff4757';
            confirmInput.style.backgroundColor = '#fff5f5';
        } else {
            confirmInput.style.borderColor = '#e1e1e1';
            confirmInput.style.backgroundColor = '#fafafa';
        }
    }

    // Show success/error messages
    showMessage(text, type) {
        const messageContainer = document.getElementById('messageContainer');
        const message = document.getElementById('message');

        message.textContent = text;
        message.className = `message ${type}`;
        messageContainer.classList.remove('hidden');

        // Auto-hide after 4 seconds
        setTimeout(() => {
            messageContainer.classList.add('hidden');
        }, 4000);
    }

    // Check authentication status
    checkAuthStatus() {
        if (this.currentUser) {
            // User is logged in, could update UI accordingly
            console.log('User is logged in:', this.currentUser.name);
        }
    }

    // Logout function (can be called from other pages)
    logout() {
        this.removeCurrentUser();
        this.showMessage('You have been logged out', 'success');
        setTimeout(() => {
            window.location.href = '/Html pages/login.html';
        }, 1500);
    }

    // Get current user (utility function)
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is logged in (utility function)
    isLoggedIn() {
        return this.currentUser !== null;
    }
}

// Initialize the auth manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
