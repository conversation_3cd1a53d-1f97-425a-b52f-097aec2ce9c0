/* Login Page Styles */
.login-section {
    min-height: 80vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 1rem;
}

.login-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(255, 107, 129, 0.1);
    padding: 3rem 2.5rem;
    width: 100%;
    max-width: 450px;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b8b, #ff8fab, #ff6b8b);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-heart {
    font-size: 3rem;
    color: #ff6b8b;
    margin-bottom: 1rem;
    animation: pulse 2s infinite ease-in-out;
}

.login-header h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.login-header p {
    color: #666;
    font-size: 1rem;
}

/* Form Styles */
.login-form, .register-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 1rem;
    color: #ff6b8b;
    z-index: 2;
}

.input-wrapper input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e1e1e1;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #ff6b8b;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 107, 129, 0.1);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 2;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #ff6b8b;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #666;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e1e1e1;
    border-radius: 4px;
    margin-right: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: #ff6b8b;
    border-color: #ff6b8b;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #ff6b8b;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #ff4757;
    text-decoration: underline;
}

/* Buttons */
.login-btn, .register-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #ff6b8b, #ff8fab);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.login-btn:hover, .register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 129, 0.3);
}

.login-btn:active, .register-btn:active {
    transform: translateY(0);
}

.btn-icon {
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon, .register-btn:hover .btn-icon {
    transform: scale(1.2);
}

/* Register/Login Links */
.register-link, .login-link {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

.register-link a, .login-link a {
    color: #ff6b8b;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.register-link a:hover, .login-link a:hover {
    color: #ff4757;
    text-decoration: underline;
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Message Container */
.message-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1000;
    max-width: 400px;
}

.message {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    color: white;
    font-weight: 500;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
}

.message.success {
    background: linear-gradient(135deg, #2ed573, #7bed9f);
}

.message.error {
    background: linear-gradient(135deg, #ff4757, #ff6b8b);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-section {
        padding: 1rem;
        min-height: 70vh;
    }
    
    .login-container {
        padding: 2rem 1.5rem;
        margin: 1rem 0;
    }
    
    .login-header h2 {
        font-size: 1.5rem;
    }
    
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .message-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 1.5rem 1rem;
    }
    
    .input-wrapper input {
        padding: 0.8rem 0.8rem 0.8rem 2.5rem;
        font-size: 0.9rem;
    }
    
    .input-wrapper i {
        left: 0.8rem;
        font-size: 0.9rem;
    }
    
    .password-toggle {
        right: 0.8rem;
    }
}
