<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customize Your Product | With Love</title>
    <link rel="stylesheet" href="../Style/styles.css">
    <link rel="stylesheet" href="../Style/customize.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Favicons -->
    <link rel="icon" type="image/png" href="../images/logo.png?ver=1.0" sizes="32x32">
    <link rel="icon" type="image/png" href="../images/logo.png?ver=1.0" sizes="16x16">
    <link rel="shortcut icon" href="../images/logo.png?ver=1.0">
    <!-- Apple Touch Icon (for iOS devices) -->
    <link rel="apple-touch-icon" href="../images/logo.png">
</head>
<body>
    <div class="loader">
        <div class="heart">❤</div>
        <div class="loading-text">Loading</div>
    </div>
    <div class="container">
        <header>
            <div class="logo">
                <img src="../images/logo.png" alt="With Love Logo">
            </div>
            <div class="title">With Love</div>
           <div class="search">
               <button class="search-toggle" id="searchToggle" aria-label="Search">
                    Search
                </button>
                <div class="search-container" id="searchContainer">
                    <div class="search-input-wrapper">
                        <input type="text" id="searchInput" class="search-input" placeholder="Search for products..." autocomplete="off">
                        <i class="fas fa-search search-icon"></i>
                        <div id="searchSuggestions" class="search-suggestions"></div>
                    </div>
                </div>
               <div class="hamburger" id="hamburger">
                   <span class="bar"></span>
                   <span class="bar"></span>
                   <span class="bar"></span>
               </div>
           </div>
            <nav class="nav-menu" id="navMenu">
                <ul>
                    <li><a href="../Html pages/index.html">Home</a></li>
                    <li><a href="../Html pages/about.html">About</a></li>
                    <li><a href="../Html pages/shop.html">Shop</a></li>
                    <li><a href="../Html pages/contact.html">Contact</a></li>
                </ul>
            </nav>
        </header>

    <main class="customize-container">
        <div class="product-preview">
            <h1 id="product-title" data-i18n="customizeTitle">Customize Your Magnet</h1>
            <div class="preview-area">
                <div class="magnet-display" id="magnet-display">
                    <div class="magnet-base">
                        <div class="magnet-template">
                            <div class="upload-area-container" id="upload-container">
                                <div class="upload-placeholder" id="upload-placeholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p data-i18n="uploadDesign">Click or drop image here</p>
                                    <p class="dimensions" data-i18n="dimensions">11cm × 9cm</p>
                                </div>
                                <div class="image-preview" id="image-preview"></div>
                                <input type="file" id="image-upload" accept="image/*" style="display: none;">
                            </div>
                            <div class="template-bottom">
                                <div class="template-design">
                                    <img id="english-text-white" src="../images/english text white.png" alt="English Text White" style="max-width: 100%; height: auto; display: block;">
                                    <img id="english-text-black" src="../images/english red black.png" alt="English Text Black" style="max-width: 100%; height: auto; display: none;">
                                    <img id="malti-text-white" src="../images/malti red text white.png" alt="Malti Text White" style="max-width: 100%; height: auto; display: none;">
                                    <img id="malti-text-black" src="../images/malti red text black.png" alt="Malti Text Black" style="max-width: 100%; height: auto; display: none;">
                                    <img id="fathers-day-white" src="../images/fathers day bottom.png" alt="Father's Day White" style="max-width: 100%; height: auto; display: none;">
                                    <img id="fathers-day-black" src="../images/blue english black.png" alt="Father's Day Black" style="max-width: 100%; height: auto; display: none;">
                                    <img id="malti-blue-white" src="../images/blue malti white.png" alt="Malti Blue White" style="max-width: 100%; height: auto; display: none;">
                                    <img id="malti-blue-black" src="../images/blue malti black.png" alt="Malti Blue Black" style="max-width: 100%; height: auto; display: none;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="preview-controls">
                    <button id="rotate-left"><i class="fas fa-undo"></i> <span data-i18n="rotateLeft">Rotate Left</span></button>
                    <button id="rotate-right"><i class="fas fa-redo"></i> <span data-i18n="rotateRight">Rotate Right</span></button>
                    <button id="zoom-in"><i class="fas fa-search-plus"></i> <span data-i18n="zoomIn">Zoom In</span></button>
                    <button id="zoom-out"><i class="fas fa-search-minus"></i> <span data-i18n="zoomOut">Zoom Out</span></button>
                </div>
            </div>
        </div>

        <div class="customization-options">
            <div class="option-section">
                <h3>Upload Your Design</h3>
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Drag & drop your image here or click to browse</p>
                    <input type="file" id="image-upload" accept="image/*" style="display: none;">
                </div>
            </div>

            <div class="option-section">
                <h3 data-i18n="magnetColor">Magnet Color</h3>
                <div class="color-options">
                    <div class="color-option selected" data-color="#ff0000" data-image="english-text" style="background-color: #ff0000;"></div>
                    <div class="color-option" data-color="#0000ff" data-image="fathers-day" style="background-color: #0000ff;"></div>
                </div>
            </div>

            <div class="option-section">
                <h3 data-i18n="textColor">Text Color</h3>
                <div class="color-options">
                    <div class="text-color-option" data-text-color="#000000" style="background-color: #000000; color: white; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border-radius: 50%; margin: 0 5px; cursor: pointer;">A</div>
                    <div class="text-color-option" data-text-color="#ffffff" style="background-color: #ffffff; color: black; border: 1px solid #ddd; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border-radius: 50%; margin: 0 5px; cursor: pointer;">A</div>
                </div>
            </div>

            <div class="option-section">
                <h3 data-i18n="size">Size</h3>
                <select id="size-selector" class="form-control">
                    <option value="small">Small (3x3 in)</option>
                    <option value="medium" selected>Medium (4x4 in)</option>
                    <option value="large">Large (5x5 in)</option>
                    <option value="custom">Custom Size</option>
                </select>
            </div>

            <div class="option-section">
                <h3 data-i18n="language">Language</h3>
                <div class="language-options">
                    <div class="language-option selected" data-lang="en">
                        <span>English</span>
                    </div>
                    <div class="language-option" data-lang="mt">
                        <span>Maltese</span>
                    </div>
                </div>
            </div>

            <div class="option-section">
                <h3 data-i18n="quantity">Quantity</h3>
                <div class="quantity-selector">
                    <button id="decrease-qty">-</button>
                    <input type="number" id="quantity" value="1" min="1" max="100">
                    <button id="increase-qty">+</button>
                </div>
            </div>

            <div class="price-section">
                <span class="price">$12.99</span>
                <button class="add-to-cart-btn" id="addToCartBtn" data-i18n="addToCart">Add to Cart</button>
            </div>
        </div>
    </main>

    </main>

    <footer>
        <div class="contact">
            <h3>Contact Us</h3>
            <p><a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank" rel="noopener noreferrer"><i class="fas fa-envelope"></i> <EMAIL></a></p>
            <p><a href="tel:+35679652171"><i class="fas fa-phone-alt"></i> +356 79652171</a></p>
        </div>
        <div class="social">
            <h3>Follow Us</h3>
            <div class="social-icons">
                <a href="https://www.facebook.com" target="_blank" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                <a href="https://www.instagram.com" target="_blank" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                <a href="https://www.messenger.com" target="_blank" aria-label="Messenger"><i class="fab fa-facebook-messenger"></i></a>
            </div>
        </div>
        <div class="newsletter">
            <h3>Newsletter</h3>
            <form id="newsletterForm" novalidate>
                <input type="hidden" name="_subject" value="New newsletter subscription">
                <input type="hidden" name="_template" value="table">
                <input type="email" name="email" placeholder="Your email">
                <button type="submit">Subscribe</button>
            </form>
        </div>
    </footer>
</div>

<script>
    // Cart functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Add to cart functionality
        const addToCartBtn = document.getElementById('addToCartBtn');
        const cartUploadArea = document.getElementById('upload-area');
        const cartImagePreview = document.querySelector('.image-preview');
        const quantityInput = document.getElementById('quantity');
        
        addToCartBtn.addEventListener('click', function() {
            const selectedColor = document.querySelector('.color-option.selected');
            const selectedTextColor = document.querySelector('.text-color-option.selected');
            const language = document.querySelector('.language-option.selected').getAttribute('data-lang');
            const quantity = parseInt(quantityInput.value) || 1;
            const price = 12.99;
            
            // Get the uploaded image if any
            const uploadedImage = cartImagePreview ? cartImagePreview.querySelector('img') : null;
            let imageDataUrl = '';
            if (uploadedImage && uploadedImage.src) {
                imageDataUrl = uploadedImage.src;
            }
            
            // Create cart item
            const cartItem = {
                id: Date.now().toString(),
                type: 'custom_magnet',
                image: imageDataUrl,
                magnetColor: selectedColor ? selectedColor.getAttribute('data-color') : '#ff0000',
                textColor: selectedTextColor ? selectedTextColor.getAttribute('data-text-color') : '#000000',
                language: language || 'en',
                quantity: quantity,
                price: price,
                total: (price * quantity).toFixed(2),
                addedAt: new Date().toISOString()
            };
            
            // Add to cart in localStorage
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            cart.push(cartItem);
            localStorage.setItem('cart', JSON.stringify(cart));
            
            // Update cart count
            updateCartCount();
            
            // Redirect to cart page
            window.location.href = '../Html pages/cart.html';
        });
        
        // Function to update cart count in header
        function updateCartCount() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
            document.querySelectorAll('.cart-count').forEach(el => {
                el.textContent = cartCount;
            });
        }
        
        // Initialize cart count
        updateCartCount();
        
        // Magnet color and text color functionality
        // Handle magnet color and text color selection
        const colorOptions = document.querySelectorAll('.color-option');
        const textColorOptions = document.querySelectorAll('.text-color-option');
        
        // Function to update the displayed image based on current selections
        function updateDisplayedImage() {
            const selectedColor = document.querySelector('.color-option.selected');
            const selectedTextColor = document.querySelector('.text-color-option.selected');
            
            if (!selectedColor) return;
            
            const color = selectedColor.getAttribute('data-color');
            const isEnglish = document.querySelector('.language-option[data-lang="en"].selected') !== null;
            
            // Hide all images first
            document.querySelectorAll('.template-design img').forEach(img => {
                img.style.display = 'none';
            });
            
            // Show the appropriate image based on selections
            if (color === '#ff0000') {
                if (isEnglish) {
                    // For red magnet with English text
                    if (selectedTextColor && selectedTextColor.getAttribute('data-text-color') === '#ffffff') {
                        document.getElementById('english-text-white').style.display = 'block';
                    } else {
                        document.getElementById('english-text-black').style.display = 'block';
                    }
                } else {
                    // For red magnet with Maltese text
                    if (selectedTextColor && selectedTextColor.getAttribute('data-text-color') === '#ffffff') {
                        document.getElementById('malti-text-white').style.display = 'block';
                    } else {
                        document.getElementById('malti-text-black').style.display = 'block';
                    }
                }
            } else if (color === '#0000ff') {
                if (isEnglish) {
                    // For blue magnet with English text
                    if (selectedTextColor && selectedTextColor.getAttribute('data-text-color') === '#ffffff') {
                        document.getElementById('fathers-day-white').style.display = 'block';
                    } else {
                        document.getElementById('fathers-day-black').style.display = 'block';
                    }
                } else {
                    // For blue magnet with Maltese text
                    if (selectedTextColor && selectedTextColor.getAttribute('data-text-color') === '#ffffff') {
                        document.getElementById('malti-blue-white').style.display = 'block';
                    } else {
                        document.getElementById('malti-blue-black').style.display = 'block';
                    }
                }
            }
        }
        
        // Handle magnet color selection
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all color options
                colorOptions.forEach(opt => opt.classList.remove('selected'));
                // Add selected class to clicked option
                this.classList.add('selected');
                
                updateDisplayedImage();
            });
        });
        
        // Handle text color selection
        textColorOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all text color options
                textColorOptions.forEach(opt => opt.classList.remove('selected'));
                // Add selected class to clicked option
                this.classList.add('selected');
                
                updateDisplayedImage();
            });
        });
        
        // Handle language selection
        const languageOptions = document.querySelectorAll('.language-option');
        languageOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all language options
                languageOptions.forEach(opt => opt.classList.remove('selected'));
                // Add selected class to clicked option
                this.classList.add('selected');
                
                updateDisplayedImage();
            });
        });
        
        // Image upload functionality
        const uploadArea = document.getElementById('upload-area');
        const uploadInput = document.getElementById('image-upload');
        const imagePreview = document.getElementById('image-preview');
        const uploadPlaceholder = document.getElementById('upload-placeholder');

        // Handle click on upload area
        uploadArea.addEventListener('click', () => {
            uploadInput.click();
        });

        // Handle drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('image/')) {
                handleImageUpload(file);
            }
        });

        // Handle file input change
        uploadInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleImageUpload(file);
            }
        });

        function handleImageUpload(file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                // Hide the upload placeholder
                if (uploadPlaceholder) {
                    uploadPlaceholder.style.display = 'none';
                }
                
                // Create and display the image preview
                const img = document.createElement('img');
                img.src = e.target.result;
                img.alt = 'Uploaded design';
                img.style.maxWidth = '100%';
                img.style.maxHeight = '100%';
                
                // Clear previous preview and add new one
                imagePreview.innerHTML = '';
                imagePreview.appendChild(img);
                imagePreview.style.display = 'block';
            };
            
            reader.readAsDataURL(file);
        }
    });
</script>

<!-- Add any other scripts here -->
<script src="../js/script.js"></script>
    <script src="customize.js"></script>
    <script type="module" src="search-animated.js"></script>
    <script type="module" src="subscribe.js"></script>
</body>
</html>
