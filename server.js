const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const validator = require('validator');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// Security middleware
app.use(helmet({
    contentSecurityPolicy: false, // Disable for development
    crossOriginEmbedderPolicy: false
}));

app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000', 'file://'],
    credentials: true
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Stricter rate limiting for auth endpoints
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 requests per windowMs
    message: 'Too many authentication attempts, please try again later.'
});

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static('.'));

// Database setup
const db = new sqlite3.Database('./database/users.db', (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
    } else {
        console.log('Connected to SQLite database.');
        initializeDatabase();
    }
});

// Initialize database tables
function initializeDatabase() {
    const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            is_active BOOLEAN DEFAULT 1,
            email_verified BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `;

    const createSessionsTable = `
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token TEXT NOT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `;

    db.run(createUsersTable, (err) => {
        if (err) {
            console.error('Error creating users table:', err.message);
        } else {
            console.log('Users table ready.');
        }
    });

    db.run(createSessionsTable, (err) => {
        if (err) {
            console.error('Error creating sessions table:', err.message);
        } else {
            console.log('Sessions table ready.');
        }
    });
}

// Utility functions
function validateEmail(email) {
    return validator.isEmail(email) && email.length <= 255;
}

function validatePassword(password) {
    return password && password.length >= 6 && password.length <= 128;
}

function validateName(name) {
    return name && name.trim().length >= 2 && name.trim().length <= 100;
}

// Middleware to verify JWT token
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}

// API Routes

// User Registration
app.post('/api/register', authLimiter, async (req, res) => {
    try {
        const { name, email, password, confirmPassword } = req.body;

        // Validation
        if (!validateName(name)) {
            return res.status(400).json({ error: 'Name must be between 2 and 100 characters' });
        }

        if (!validateEmail(email)) {
            return res.status(400).json({ error: 'Please provide a valid email address' });
        }

        if (!validatePassword(password)) {
            return res.status(400).json({ error: 'Password must be between 6 and 128 characters' });
        }

        if (password !== confirmPassword) {
            return res.status(400).json({ error: 'Passwords do not match' });
        }

        // Check if user already exists
        const existingUser = await new Promise((resolve, reject) => {
            db.get('SELECT id FROM users WHERE email = ?', [email.toLowerCase()], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (existingUser) {
            return res.status(400).json({ error: 'An account with this email already exists' });
        }

        // Hash password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Insert new user
        const result = await new Promise((resolve, reject) => {
            db.run(
                'INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
                [name.trim(), email.toLowerCase(), hashedPassword],
                function(err) {
                    if (err) reject(err);
                    else resolve({ id: this.lastID });
                }
            );
        });

        res.status(201).json({
            success: true,
            message: 'Account created successfully',
            userId: result.id
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Internal server error during registration' });
    }
});

// User Login
app.post('/api/login', authLimiter, async (req, res) => {
    try {
        const { email, password, rememberMe } = req.body;

        // Validation
        if (!validateEmail(email)) {
            return res.status(400).json({ error: 'Please provide a valid email address' });
        }

        if (!password) {
            return res.status(400).json({ error: 'Password is required' });
        }

        // Find user
        const user = await new Promise((resolve, reject) => {
            db.get(
                'SELECT id, name, email, password, is_active FROM users WHERE email = ?',
                [email.toLowerCase()],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!user) {
            return res.status(401).json({ error: 'Invalid email or password' });
        }

        if (!user.is_active) {
            return res.status(401).json({ error: 'Account is deactivated' });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid email or password' });
        }

        // Generate JWT token
        const tokenExpiry = rememberMe ? '30d' : '24h';
        const token = jwt.sign(
            { 
                userId: user.id, 
                email: user.email,
                name: user.name 
            },
            JWT_SECRET,
            { expiresIn: tokenExpiry }
        );

        // Update last login
        db.run(
            'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
            [user.id]
        );

        // Store session (optional, for logout functionality)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + (rememberMe ? 30 : 1));
        
        db.run(
            'INSERT INTO user_sessions (user_id, token, expires_at) VALUES (?, ?, ?)',
            [user.id, token, expiresAt.toISOString()]
        );

        res.json({
            success: true,
            message: 'Login successful',
            token: token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error during login' });
    }
});

// Verify Token (for checking if user is still logged in)
app.get('/api/verify', authenticateToken, (req, res) => {
    res.json({
        success: true,
        user: {
            id: req.user.userId,
            name: req.user.name,
            email: req.user.email
        }
    });
});

// User Logout
app.post('/api/logout', authenticateToken, (req, res) => {
    const token = req.headers['authorization'].split(' ')[1];
    
    // Remove session from database
    db.run('DELETE FROM user_sessions WHERE token = ?', [token], (err) => {
        if (err) {
            console.error('Logout error:', err);
        }
    });

    res.json({ success: true, message: 'Logged out successfully' });
});

// Get User Profile
app.get('/api/profile', authenticateToken, async (req, res) => {
    try {
        const user = await new Promise((resolve, reject) => {
            db.get(
                'SELECT id, name, email, registration_date, last_login FROM users WHERE id = ?',
                [req.user.userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json({
            success: true,
            user: user
        });

    } catch (error) {
        console.error('Profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Admin panel for viewing users (simple web interface)
app.get('/admin/users', async (req, res) => {
    try {
        const users = await new Promise((resolve, reject) => {
            db.all(`
                SELECT id, name, email, registration_date, last_login, is_active, email_verified
                FROM users
                ORDER BY registration_date DESC
            `, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        const sessionStats = await new Promise((resolve, reject) => {
            db.get(`
                SELECT
                    COUNT(*) as total_sessions,
                    COUNT(CASE WHEN expires_at > datetime('now') THEN 1 END) as active_sessions
                FROM user_sessions
            `, [], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        const html = generateAdminHTML(users, sessionStats);
        res.send(html);
    } catch (error) {
        console.error('Admin panel error:', error);
        res.status(500).send('Error loading admin panel');
    }
});

// Generate admin panel HTML
function generateAdminHTML(users, sessionStats) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>User Management - Julia's Magnet Website</title>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #a67f83; text-align: center; margin-bottom: 30px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
            .stat-card { background: #f0d9d9; padding: 20px; border-radius: 8px; text-align: center; }
            .stat-number { font-size: 2em; font-weight: bold; color: #a67f83; }
            .stat-label { color: #666; margin-top: 5px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background: #a67f83; color: white; }
            tr:hover { background: #f9f9f9; }
            .status { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
            .active { background: #d4edda; color: #155724; }
            .inactive { background: #f8d7da; color: #721c24; }
            .verified { color: #28a745; }
            .unverified { color: #dc3545; }
            .back-link { display: inline-block; margin-bottom: 20px; color: #a67f83; text-decoration: none; }
            .back-link:hover { text-decoration: underline; }
            .refresh-btn { background: #a67f83; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; float: right; }
            .refresh-btn:hover { background: #8c6b6e; }
        </style>
    </head>
    <body>
        <div class="container">
            <a href="/" class="back-link"><i class="fas fa-arrow-left"></i> Back to Website</a>
            <button class="refresh-btn" onclick="location.reload()"><i class="fas fa-sync"></i> Refresh</button>

            <h1><i class="fas fa-users"></i> User Management Dashboard</h1>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">${users.length}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${users.filter(u => u.is_active).length}</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${users.filter(u => u.email_verified).length}</div>
                    <div class="stat-label">Verified Emails</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${sessionStats.active_sessions}</div>
                    <div class="stat-label">Active Sessions</div>
                </div>
            </div>

            ${users.length === 0 ?
                '<p style="text-align: center; color: #666; font-style: italic;">No users registered yet. Users will appear here after they sign up on your website.</p>' :
                `<table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Registered</th>
                            <th>Last Login</th>
                            <th>Status</th>
                            <th>Verified</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>${user.id}</td>
                                <td>${user.name}</td>
                                <td>${user.email}</td>
                                <td>${new Date(user.registration_date).toLocaleDateString()}</td>
                                <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</td>
                                <td><span class="status ${user.is_active ? 'active' : 'inactive'}">${user.is_active ? 'Active' : 'Inactive'}</span></td>
                                <td><i class="fas ${user.email_verified ? 'fa-check verified' : 'fa-times unverified'}"></i></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>`
            }

            <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; font-size: 0.9em; color: #666;">
                <strong>💡 Tips:</strong><br>
                • This page shows all registered users from your database<br>
                • Refresh the page to see new registrations<br>
                • Use command line tools for advanced management: <code>npm run view-users</code> or <code>npm run user-stats</code>
            </div>
        </div>
    </body>
    </html>
    `;
}

// Serve HTML pages
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'Html pages', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down gracefully...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err.message);
        } else {
            console.log('Database connection closed.');
        }
        process.exit(0);
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
    console.log(`📁 Database: ./database/users.db`);
    console.log(`🔒 JWT Secret: ${JWT_SECRET.substring(0, 10)}...`);
});

module.exports = app;
