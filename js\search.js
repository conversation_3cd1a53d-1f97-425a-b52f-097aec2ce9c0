// Search functionality for the website
const searchInput = document.getElementById('searchInput');
const searchIcon = document.querySelector('.search-icon');
const searchContainer = document.getElementById('searchContainer');
const suggestionsContainer = document.getElementById('searchSuggestions');

// Keywords for search suggestions
const searchKeywords = [
    "Father's Day",
    "Father's Day Gifts",
    "Gifts for Dad",
    "Personalized Father's Day",
    "Father's Day Magnets",
    "Best Dad Ever",
    "Father's Day 2024",
    "Handmade Father's Day Gifts",
    "Unique Gifts for Dad",
    "Father's Day Presents"
];

// Add event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initSearch();
});

function initSearch() {
    // Show/hide search on mobile
    const searchToggle = document.getElementById('searchToggle');
    if (searchToggle) {
        searchToggle.addEventListener('click', toggleSearch);
    }
    
    // Handle search input
    if (searchInput) {
        searchInput.addEventListener('input', handleSearchInput);
        searchInput.addEventListener('focus', showSuggestions);
        
        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!searchContainer.contains(e.target)) {
                hideSuggestions();
            }
        });
    }
    
    // Handle search icon click
    if (searchIcon) {
        searchIcon.addEventListener('click', () => {
            if (searchInput.value.trim()) {
                performSearch(searchInput.value.trim());
            }
        });
    }
    
    // Handle Enter key press
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && searchInput.value.trim()) {
                performSearch(searchInput.value.trim());
            }
        });
    }
}

function toggleSearch() {
    searchContainer.classList.toggle('active');
    if (searchContainer.classList.contains('active')) {
        searchInput.focus();
    }
}

function handleSearchInput(e) {
    const query = e.target.value.trim().toLowerCase();
    if (query.length > 0) {
        showSuggestions(query);
    } else {
        hideSuggestions();
    }
}

function showSuggestions(query = '') {
    if (!suggestionsContainer) return;
    
    // Clear previous suggestions
    suggestionsContainer.innerHTML = '';
    
    // Get matching products and keywords
    const productMatches = getProductMatches(query);
    const keywordMatches = getKeywordMatches(query);
    
    // Show message if no matches found
    if (productMatches.length === 0 && keywordMatches.length === 0) {
        const noResults = document.createElement('div');
        noResults.className = 'no-suggestions';
        noResults.textContent = 'No matching products found';
        suggestionsContainer.appendChild(noResults);
    } else {
        // Add keyword suggestions first
        if (keywordMatches.length > 0) {
            const keywordHeader = document.createElement('div');
            keywordHeader.className = 'suggestion-header';
            keywordHeader.textContent = 'Suggestions';
            suggestionsContainer.appendChild(keywordHeader);
            
            keywordMatches.forEach(keyword => {
                const suggestion = createSuggestionItem({
                    text: keyword,
                    isKeyword: true
                });
                suggestionsContainer.appendChild(suggestion);
            });
        }
        
        // Add product matches
        if (productMatches.length > 0) {
            const productHeader = document.createElement('div');
            productHeader.className = 'suggestion-header';
            productHeader.textContent = 'Products';
            if (keywordMatches.length > 0) {
                productHeader.style.marginTop = '10px';
            }
            suggestionsContainer.appendChild(productHeader);
            
            productMatches.slice(0, 5).forEach(product => {
                const suggestion = createSuggestionItem({
                    text: product.name,
                    image: product.image,
                    product: product
                });
                suggestionsContainer.appendChild(suggestion);
            });
        }
    }
    
    // Show suggestions container
    suggestionsContainer.classList.add('visible');
}

function hideSuggestions() {
    if (suggestionsContainer) {
        suggestionsContainer.classList.remove('visible');
    }
}

function getProductMatches(query) {
    if (!query) return [];
    return products.filter(product => 
        product.name.toLowerCase().includes(query) ||
        product.category.toLowerCase().includes(query) ||
        product.color.toLowerCase().includes(query)
    );
}

function getKeywordMatches(query) {
    if (!query) return [];
    return searchKeywords.filter(keyword => 
        keyword.toLowerCase().includes(query)
    ).slice(0, 5);
}

function createSuggestionItem({ text, image, isKeyword = false, product = null }) {
    const item = document.createElement('div');
    item.className = 'suggestion-item';
    
    if (image) {
        const img = document.createElement('img');
        img.src = image;
        img.alt = text;
        item.appendChild(img);
    } else {
        const icon = document.createElement('i');
        icon.className = isKeyword ? 'fas fa-search' : 'fas fa-gift';
        item.appendChild(icon);
    }
    
    const textSpan = document.createElement('span');
    textSpan.textContent = text;
    item.appendChild(textSpan);
    
    item.addEventListener('click', () => {
        if (product) {
            // Navigate to product page
            window.location.href = `product.html?id=${product.id}`;
        } else {
            // Update search input with keyword
            searchInput.value = text;
            performSearch(text);
        }
    });
    
    return item;
}

function performSearch(query) {
    if (!query) return;
    
    // Check if it's a direct product match
    const directMatch = products.find(p => 
        p.name.toLowerCase() === query.toLowerCase()
    );
    
    if (directMatch) {
        window.location.href = `../Product pages/product.html?id=${directMatch.id}`;
    } else {
        // Handle keyword search
        window.location.href = `../Html pages/search-results.html?q=${encodeURIComponent(query)}`;
    }
}
