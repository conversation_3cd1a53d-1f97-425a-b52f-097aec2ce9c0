<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customize Calendar Magnet | With Love</title>
    <link rel="stylesheet" href="../Style/styles.css">
    <link rel="stylesheet" href="../Style/customize.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Favicons -->
    <link rel="icon" type="image/png" href="../images/logo.png?ver=1.0" sizes="32x32">
    <link rel="icon" type="image/png" href="../images/logo.png?ver=1.0" sizes="16x16">
    <link rel="shortcut icon" href="../images/logo.png?ver=1.0">
    <!-- Apple Touch Icon (for iOS devices) -->
    <link rel="apple-touch-icon" href="../images/logo.png">
</head>
<body>
    <div class="loader">
        <div class="heart">❤</div>
        <div class="loading-text">Loading</div>
    </div>
    <div class="container">
        <header>
            <div class="logo">
                <img src="../images/logo.png" alt="With Love Logo">
            </div>
            <div class="title">With Love</div>
            <div class="search">
                <button class="search-toggle" id="searchToggle" aria-label="Search">
                    Search
                </button>
                <div class="search-container" id="searchContainer">
                    <input type="text" class="search-input" placeholder="Search...">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="hamburger" id="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
            <nav class="nav-menu" id="navMenu">
                <ul>
                    <li><a href="../Html pages/index.html">Home</a></li>
                    <li><a href="../Html pages/about.html">About</a></li>
                    <li><a href="../Html pages/shop.html">Shop</a></li>
                    <li><a href="../Html pages/contact.html">Contact</a></li>
                </ul>
            </nav>
        </header>

        <main class="customize-container">
            <div class="product-preview">
                <h1 id="product-title" data-i18n="customizeTitle">Customize Calendar Magnet</h1>
                <div class="preview-area">
                    <div class="magnet-display" id="magnet-display">
                        <div class="magnet-base">
                            <div class="magnet-template">
                                <div class="upload-area-container" id="upload-container">
                                    <div class="upload-placeholder" id="upload-placeholder">
                                        <i class="fas fa-calendar-alt"></i>
                                        <p>Click or drop image here</p>
                                        <p class="dimensions">15cm × 10cm</p>
                                    </div>
                                    <div class="image-preview" id="image-preview"></div>
                                    <input type="file" id="image-upload" accept="image/*" style="display: none;">
                                </div>
                                <div class="template-bottom">
                                    <div class="template-design">
                                        <img id="calendar-red" src="images/calendar-red.png" alt="Calendar Red" style="max-width: 100%; height: auto; display: block;">
                                        <img id="calendar-blue" src="images/calendar-blue.png" alt="Calendar Blue" style="max-width: 100%; height: auto; display: none;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="preview-controls">
                        <button id="rotate-left"><i class="fas fa-undo"></i> <span>Rotate Left</span></button>
                        <button id="rotate-right"><i class="fas fa-redo"></i> <span>Rotate Right</span></button>
                        <button id="zoom-in"><i class="fas fa-search-plus"></i> <span>Zoom In</span></button>
                        <button id="zoom-out"><i class="fas fa-search-minus"></i> <span>Zoom Out</span></button>
                    </div>
                </div>
            </div>

            <div class="customization-options">
                <div class="option-section">
                    <h3>Upload Your Design</h3>
                    <div class="upload-area" id="upload-area">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Drag & drop your image here or click to browse</p>
                        <input type="file" id="image-upload" accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="option-section">
                    <h3>Calendar Color</h3>
                    <div class="color-options">
                        <div class="color-option selected" data-color="#ff0000" data-image="calendar-red" style="background-color: #ff0000;"></div>
                        <div class="color-option" data-color="#0000ff" data-image="calendar-blue" style="background-color: #0000ff;"></div>
                    </div>
                </div>

                <div class="option-section">
                    <h3>Month</h3>
                    <select id="month-selector" class="form-control">
                        <option value="january">January</option>
                        <option value="february">February</option>
                        <option value="march" selected>March</option>
                        <option value="april">April</option>
                        <option value="may">May</option>
                        <option value="june">June</option>
                        <option value="july">July</option>
                        <option value="august">August</option>
                        <option value="september">September</option>
                        <option value="october">October</option>
                        <option value="november">November</option>
                        <option value="december">December</option>
                    </select>
                </div>

                <div class="option-section">
                    <h3>Year</h3>
                    <select id="year-selector" class="form-control">
                        <option value="2023">2023</option>
                        <option value="2024">2024</option>
                        <option value="2025" selected>2025</option>
                        <option value="2026">2026</option>
                    </select>
                </div>

                <div class="option-section">
                    <h3>Quantity</h3>
                    <div class="quantity-selector">
                        <button id="decrease-qty">-</button>
                        <input type="number" id="quantity" value="1" min="1" max="100">
                        <button id="increase-qty">+</button>
                    </div>
                </div>

                <div class="price-section">
                    <span class="price">$16.99</span>
                    <button class="add-to-cart-btn">Add to Cart</button>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <div class="contact">
            <h3>Contact Us</h3>
            <p><a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank" rel="noopener noreferrer"><i class="fas fa-envelope"></i> <EMAIL></a></p>
            <p><a href="tel:+35679652171"><i class="fas fa-phone-alt"></i> +356 79652171</a></p>
        </div>
        <div class="social">
            <h3>Follow Us</h3>
            <div class="social-icons">
                <a href="https://www.facebook.com" target="_blank" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                <a href="https://www.instagram.com" target="_blank" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                <a href="https://www.messenger.com" target="_blank" aria-label="Messenger"><i class="fab fa-facebook-messenger"></i></a>
            </div>
        </div>
        <div class="newsletter">
            <h3>Newsletter</h3>
            <form id="newsletterForm" novalidate>
                <input type="hidden" name="_subject" value="New newsletter subscription">
                <input type="hidden" name="_template" value="table">
                <input type="email" name="email" placeholder="Your email">
                <button type="submit">Subscribe</button>
            </form>
        </div>
    </footer>

    <script>
        // Magnet color functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle color selection
            const colorOptions = document.querySelectorAll('.color-option');
            
            colorOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selected class from all color options
                    colorOptions.forEach(opt => opt.classList.remove('selected'));
                    // Add selected class to clicked option
                    this.classList.add('selected');
                    
                    // Update displayed image
                    const imageId = this.getAttribute('data-image');
                    document.querySelectorAll('.template-design img').forEach(img => {
                        img.style.display = 'none';
                    });
                    document.getElementById(imageId).style.display = 'block';
                });
            });
            
            // Handle image upload
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('image-upload');
            const imagePreview = document.getElementById('image-preview');
            const uploadPlaceholder = document.getElementById('upload-placeholder');
            
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('drag-over');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                
                if (e.dataTransfer.files.length) {
                    fileInput.files = e.dataTransfer.files;
                    handleImageUpload(e.dataTransfer.files[0]);
                }
            });
            
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length) {
                    handleImageUpload(e.target.files[0]);
                }
            });
            
            function handleImageUpload(file) {
                const reader = new FileReader();
                
                reader.onload = (e) => {
                    uploadPlaceholder.style.display = 'none';
                    imagePreview.innerHTML = '';
                    
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    imagePreview.appendChild(img);
                    imagePreview.style.display = 'block';
                };
                
                reader.readAsDataURL(file);
            }
            
            // Handle quantity buttons
            const quantityInput = document.getElementById('quantity');
            document.getElementById('increase-qty').addEventListener('click', () => {
                quantityInput.value = Math.min(parseInt(quantityInput.value) + 1, 100);
            });
            
            document.getElementById('decrease-qty').addEventListener('click', () => {
                quantityInput.value = Math.max(parseInt(quantityInput.value) - 1, 1);
            });
        });
    </script>

    <!-- Add any other scripts here -->
    <script src="../js/script.js"></script>
    <script src="customize.js"></script>
</body>
</html>
