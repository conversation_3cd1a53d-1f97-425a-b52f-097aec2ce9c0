// Debug script to check cart contents
document.addEventListener('DOMContentLoaded', function() {
    // Clear any existing cart data
    localStorage.removeItem('cart');
    console.log('Cart cleared');
    
    // Check cart contents after a short delay
    setTimeout(() => {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        console.log('Current cart contents:', cart);
        
        if (cart.length > 0) {
            console.warn('Cart is not empty after clearing!');
        } else {
            console.log('Cart is empty as expected');
        }
    }, 1000);
});
